package com.example.springboot.tool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.math3.distribution.FDistribution;
import org.apache.commons.math3.distribution.TDistribution;
import org.apache.commons.math3.linear.EigenDecomposition;
import org.apache.commons.math3.linear.LUDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.linear.SingularMatrixException;
import org.apache.commons.math3.stat.correlation.KendallsCorrelation;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.commons.math3.stat.correlation.SpearmansCorrelation;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import com.example.springboot.entity.AiChatMessage;
import com.example.springboot.entity.AnalysisResult;
import com.example.springboot.entity.DataQuery;
import com.example.springboot.entity.FactorAnalysisOutput;
import com.example.springboot.entity.SurveyData;
import com.example.springboot.entity.WjxSurveyData;
import com.example.springboot.mapper.AIChatMessageMapper;
import com.example.springboot.service.impl.AIChatServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.math3.stat.inference.TTest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class AdjustDataTools {

    private final AIChatServiceImpl aiChatService;

    private final AIChatMessageMapper messageMapper;

    // 问卷结构缓存
    private final Map<String, List<SurveyStructure>> surveyStructureCache = new ConcurrentHashMap<>();

    // 问卷结构信息类
    public static class SurveyStructure {
        private Integer numId; // 题号
        private String title; // 题目标题
        private String type; // 题型编号
        private String typeInfo; // 题型名称
        private List<String> options; // 选项内容
        private List<Integer> colIndices; // EXCEL表格的列角标（从1开始）
        private List<SurveyData.SubQuestion> subQuestions; // 矩阵题的小题信息

        public Integer getNumId() {
            return numId;
        }

        public void setNumId(Integer numId) {
            this.numId = numId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTypeInfo() {
            return typeInfo;
        }

        public void setTypeInfo(String typeInfo) {
            this.typeInfo = typeInfo;
        }

        public List<String> getOptions() {
            return options;
        }

        public void setOptions(List<String> options) {
            this.options = options;
        }

        public List<Integer> getColIndices() {
            return colIndices;
        }

        public void setColIndices(List<Integer> colIndices) {
            this.colIndices = colIndices;
        }

        public List<SurveyData.SubQuestion> getSubQuestions() {
            return subQuestions;
        }

        public void setSubQuestions(List<SurveyData.SubQuestion> subQuestions) {
            this.subQuestions = subQuestions;
        }
    }

    @lombok.Data
    public static class RangeContext {
        private List<SurveyData> structures;
        private List<List<String>> data;
        private int startRow; // 2-based
        private int startCol; // 1-based
        private int endRow; // 2-based
        private int endCol; // 1-based
        private List<String> selectedHeaders; // 新增：用于存储所选范围的表头
    }

    @Tool(description = "设置问卷结构信息，便于后续分析工具准确识别题目和选项")
    public List<SurveyStructure> setSurveyStructure(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "问卷结构信息") List<Map<String, Object>> structureList) {
        log.info("[setSurveyStructure] 收到参数: {}", structureList);
        if (structureList == null) {
            log.error("[setSurveyStructure] 传入的structureList为null");
            throw new IllegalArgumentException("问卷结构信息不能为空，必须为List<Map<String, Object>>格式");
        }
        List<SurveyStructure> converted = new ArrayList<>();
        int idx = 0;
        ObjectMapper mapper = new ObjectMapper();
        for (Object obj : structureList) {
            if (!(obj instanceof Map)) {
                log.error("[setSurveyStructure] 第{}项不是Map，实际类型:{}，内容:{}", idx, obj == null ? "null" : obj.getClass(),
                        obj);
                throw new IllegalArgumentException("问卷结构信息每一项都必须为Map<String, Object>，请检查AI传参格式");
            }
            Map<String, Object> map = (Map<String, Object>) obj;
            SurveyStructure s = new SurveyStructure();
            s.setNumId(map.get("numId") != null ? Integer.valueOf(map.get("numId").toString()) : null);
            s.setTitle((String) map.get("title"));
            s.setType((String) map.get("type"));
            s.setTypeInfo((String) map.get("typeInfo"));
            Object optionsObj = map.get("options");
            if (optionsObj instanceof List) {
                List<String> options = new ArrayList<>();
                for (Object o : (List<?>) optionsObj)
                    options.add(o == null ? null : o.toString());
                s.setOptions(options);
            } else {
                s.setOptions(null);
            }
            Object colListObj = map.get("colIndices");
            if (colListObj instanceof List) {
                List<Integer> colIndices = new ArrayList<>();
                for (Object o : (List<?>) colListObj)
                    colIndices.add(Integer.valueOf(o.toString()));
                s.setColIndices(colIndices);
            } else {
                s.setColIndices(null);
            }

            Object subQuestionsObj = map.get("subQuestions");
            if (subQuestionsObj instanceof List) {
                List<SurveyData.SubQuestion> subQuestions = new ArrayList<>();
                for (Object sqObj : (List<?>) subQuestionsObj) {
                    if (sqObj != null) { // 过滤掉null值
                        SurveyData.SubQuestion sq = mapper.convertValue(sqObj, SurveyData.SubQuestion.class);
                        subQuestions.add(sq);
                    }
                }
                s.setSubQuestions(subQuestions.isEmpty() ? null : subQuestions);
            } else {
                s.setSubQuestions(null);
            }

            converted.add(s);
            idx++;
        }
        surveyStructureCache.put(sessionId, converted);
        return converted;
    }

    @Tool(description = "只能通过本工具查询或操作EXCEL数据，不能直接分析数据。请严格按照DataQuery参数结构传递查询或操作条件。返回结果为查询到的数据或需要操作的单元格坐标。参数示例请参考系统提示。")
    public List<List<Object>> queryOrUpdateExcel(
            @ToolParam(description = "会话ID。例如：\"e1d41bcf-87c6-42cc-86e4-fa9804a83468\"") String sessionId,
            @ToolParam(description = "查询或操作参数DataQuery，详见DataQuery结构说明和示例。") DataQuery query) {
        log.info("[EXCEL查询] sessionId={}, query={}", sessionId, query);
        List<List<Object>> result = new ArrayList<>();
        try {
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message == null || message.getCompleteExcelData() == null) {
                log.warn("[EXCEL查询] 未找到对应的Excel数据，sessionId={}", sessionId);
                throw new RuntimeException("未找到对应的Excel数据");
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(
                    message.getCompleteExcelData(),
                    new TypeReference<List<List<String>>>() {
                    });
            // 新增：处理rowNums逻辑
            List<Integer> rowNums = query.getRowNums();
            List<Integer> targetRowIndices = null;
            if (rowNums != null && !rowNums.isEmpty()) {
                int totalRows = excelData.size() - 1; // 不含表头
                targetRowIndices = new ArrayList<>();
                for (Integer rn : rowNums) {
                    int idx = 0;
                    if (rn == null)
                        continue;
                    if (rn > 0) {
                        idx = rn; // 1-based数据行号
                    } else if (rn < 0) {
                        idx = totalRows + 1 + rn; // -1=>最后一行, -2=>倒数第二行
                    }
                    if (idx >= 1 && idx <= totalRows) {
                        targetRowIndices.add(idx);
                    }
                }
            }
            for (int i = 1; i < excelData.size(); i++) {
                if (targetRowIndices != null && !targetRowIndices.contains(i))
                    continue;
                List<String> row = excelData.get(i);
                if (row == null)
                    continue;
                boolean match = true;
                if (query.getConditions() != null) {
                    for (DataQuery.Condition cond : query.getConditions()) {
                        int colIdx = cond.getCol() - 1;
                        if (colIdx < 0) {
                            colIdx = row.size() + colIdx; // 支持倒数列号
                        }
                        String cellValue = (colIdx >= 0 && colIdx < row.size()) ? row.get(colIdx) : "";
                        String op = cond.getOp();
                        Object value = cond.getValue();
                        if (!matchCondition(cellValue, op, value)) {
                            match = false;
                            break;
                        }
                    }
                }
                if (!match)
                    continue;
                log.debug("[EXCEL查询] 匹配到行: rowIndex={}, row={}", i, row);
                if (Boolean.TRUE.equals(query.getOnlyQuery())) {
                    List<Object> rowData = new ArrayList<>();
                    List<Integer> colIndices = null;
                    if (query.getReturnCols() != null && !query.getReturnCols().isEmpty()) {
                        colIndices = new ArrayList<>();
                        for (Integer col : query.getReturnCols()) {
                            int colIdx = col - 1;
                            if (colIdx < 0) {
                                colIdx = row.size() + colIdx; // 支持倒数列号
                            }
                            colIndices.add(colIdx);
                            rowData.add((colIdx >= 0 && colIdx < row.size()) ? row.get(colIdx) : "");
                        }
                    } else {
                        // 返回整行
                        colIndices = new ArrayList<>();
                        for (int colIdx = 0; colIdx < row.size(); colIdx++) {
                            rowData.add(row.get(colIdx));
                            colIndices.add(colIdx);
                        }
                    }
                    for (int j = 0; j < rowData.size(); j++) {
                        result.add(Arrays.asList(i, colIndices.get(j), rowData.get(j)));
                    }
                } else {
                    if (query.getReturnCols() != null && !query.getReturnCols().isEmpty()) {
                        for (Integer col : query.getReturnCols()) {
                            int colIdx = col - 1;
                            if (colIdx < 0) {
                                colIdx = row.size() + colIdx; // 支持倒数列号
                            }
                            result.add(Arrays.asList(i, colIdx));
                        }
                    } else {
                        for (int c = 0; c < row.size(); c++) {
                            result.add(Arrays.asList(i, c));
                        }
                    }
                }
            }
            log.info("[EXCEL查询] 查询结果: {}", result);
        } catch (Exception e) {
            log.error("[EXCEL查询] Excel处理异常", e);
            log.error("[EXCEL查询] DataQuery参数解析异常，query原始内容: {}", query, e);
            throw new RuntimeException("Excel处理异常: " + e.getMessage());
        }
        return result;
    }

    private String getCellString(Cell cell) {
        if (cell == null)
            return "";
        if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf(cell.getNumericCellValue());
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else {
            return cell.getStringCellValue();
        }
    }

    private boolean matchCondition(String cellValue, String op, Object value) {
        if (op == null)
            return false;
        switch (op) {
            case "eq":
                return cellValue.equals(String.valueOf(value));
            case "neq":
                return !cellValue.equals(String.valueOf(value));
            case "contains":
                return cellValue.contains(String.valueOf(value));
            case "gt":
                try {
                    return Double.parseDouble(cellValue) > Double.parseDouble(String.valueOf(value));
                } catch (Exception e) {
                    return false;
                }
            case "lt":
                try {
                    return Double.parseDouble(cellValue) < Double.parseDouble(String.valueOf(value));
                } catch (Exception e) {
                    return false;
                }
            case "gte":
                try {
                    return Double.parseDouble(cellValue) >= Double.parseDouble(String.valueOf(value));
                } catch (Exception e) {
                    return false;
                }
            case "lte":
                try {
                    return Double.parseDouble(cellValue) <= Double.parseDouble(String.valueOf(value));
                } catch (Exception e) {
                    return false;
                }
            case "in":
                if (value instanceof List) {
                    for (Object v : (List<?>) value) {
                        if (cellValue.equals(String.valueOf(v)))
                            return true;
                    }
                }
                return false;
            default:
                return false;
        }
    }

    private List<String> getColumnData(String sessionId, Integer columnIndex) {
        try {
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message == null || message.getCompleteExcelData() == null) {
                throw new RuntimeException("未找到对应的Excel数据");
            }

            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(
                    message.getCompleteExcelData(),
                    new TypeReference<List<List<String>>>() {
                    });

            int colIdx = columnIndex;
            return excelData.stream()
                    .skip(1) // 跳过表头
                    .map(row -> (colIdx >= 0 && colIdx < row.size()) ? row.get(colIdx) : "")
                    .collect(Collectors.toList());
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Excel数据解析异常: " + e.getMessage());
        }
    }

    protected List<Double> getNumericColumnData(String sessionId, Integer columnIndex) {
        return getColumnData(sessionId, columnIndex).stream()
                .map(value -> {
                    try {
                        return Double.parseDouble(value);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Tool(description = "计算Cronbach's Alpha信度系数，用于评估量表的内部一致性信度")
    public AnalysisResult.ReliabilityResult calculateCronbachAlpha(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要分析的列索引列表（从1开始）") List<Integer> columns) {
        log.info("[信度分析] 开始Cronbach's Alpha分析，sessionId={}, columns={}", sessionId, columns);
        try {
            // 获取多列数据
            List<List<Double>> columnData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                columnData.add(data);
            }

            int itemCount = columnData.size();
            int sampleSize = columnData.get(0).size();

            // 计算项目方差和总方差
            double totalVariance = 0.0;
            double itemVarianceSum = 0.0;
            List<Double> itemTotalCorrelations = new ArrayList<>();
            List<Double> itemDeletedAlphas = new ArrayList<>();

            // 计算总分
            List<Double> totalScores = new ArrayList<>();
            for (int i = 0; i < sampleSize; i++) {
                double total = 0.0;
                for (List<Double> col : columnData) {
                    total += col.get(i);
                }
                totalScores.add(total);
            }

            // 计算总方差
            DescriptiveStatistics totalStats = new DescriptiveStatistics();
            totalScores.forEach(totalStats::addValue);
            totalVariance = totalStats.getVariance();

            // 计算各项目方差和项目总分相关
            for (int i = 0; i < itemCount; i++) {
                DescriptiveStatistics itemStats = new DescriptiveStatistics();
                columnData.get(i).forEach(itemStats::addValue);
                itemVarianceSum += itemStats.getVariance();

                // 计算项目总分相关
                double itemTotalCorr = calculateCorrelation(columnData.get(i), totalScores);
                itemTotalCorrelations.add(itemTotalCorr);

                // 计算删除项目后的Alpha值
                double deletedVariance = totalVariance - itemStats.getVariance();
                double deletedAlpha = (itemCount - 1.0) / (itemCount - 2.0) * (1 - itemVarianceSum / deletedVariance);
                itemDeletedAlphas.add(deletedAlpha);
            }

            // 计算Cronbach's Alpha
            double cronbachAlpha = (itemCount / (itemCount - 1.0)) * (1 - itemVarianceSum / totalVariance);

            // 计算标准化Alpha
            double standardizedAlpha = calculateStandardizedAlpha(columnData);

            // 计算平均项目间相关
            double averageInterItemCorrelation = calculateAverageInterItemCorrelation(columnData);

            // 确定信度水平
            String reliabilityLevel = determineReliabilityLevel(cronbachAlpha);

            AnalysisResult.ReliabilityResult result = new AnalysisResult.ReliabilityResult();

            // ----------- 获取变量名称（表头） -----------
            List<String> variableNames = new ArrayList<>();
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(
                            message.getCompleteExcelData(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                            });
                    if (!excelData.isEmpty()) {
                        List<String> headers = excelData.get(0);
                        for (Integer col : columns) {
                            int colIdx = col - 1;
                            if (colIdx >= 0 && colIdx < headers.size()) {
                                String header = headers.get(colIdx);
                                if (header == null || header.trim().isEmpty()) {
                                    variableNames.add("变量" + col);
                                } else {
                                    variableNames.add(header.trim());
                                }
                            } else {
                                variableNames.add("变量" + col);
                            }
                        }
                    } else {
                        for (Integer col : columns) {
                            variableNames.add("变量" + col);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("[信度分析] 获取表头信息失败，使用默认变量名称: {}", e.getMessage());
                for (Integer col : columns) {
                    variableNames.add("变量" + col);
                }
            }

            // ----------- 生成三张表格 -----------
            List<FactorAnalysisOutput.TableData> tables = new ArrayList<>();
            // 1. 可靠性统计表
            FactorAnalysisOutput.TableData table1 = new FactorAnalysisOutput.TableData();
            table1.setType("reliability_alpha");
            table1.setTitle("可靠性统计");
            table1.setHeaders(Arrays.asList("克隆巴赫Alpha", "项数"));
            table1.setRows(Arrays.asList(
                    Arrays.asList(String.format("%.3f", cronbachAlpha), itemCount)));
            tables.add(table1);

            // 2. 项目统计表
            FactorAnalysisOutput.TableData table2 = new FactorAnalysisOutput.TableData();
            table2.setType("reliability_item");
            table2.setTitle("项统计");
            table2.setHeaders(Arrays.asList("标题", "平均值", "标准差", "个案数"));
            List<List<Object>> itemRows = new ArrayList<>();
            for (int i = 0; i < itemCount; i++) {
                DescriptiveStatistics stats = new DescriptiveStatistics();
                int validCount = 0;
                for (Double v : columnData.get(i)) {
                    if (v != null && !Double.isNaN(v)) {
                        stats.addValue(v);
                        validCount++;
                    }
                }
                itemRows.add(Arrays.asList(
                        variableNames.get(i),
                        String.format("%.2f", stats.getMean()),
                        String.format("%.3f", stats.getStandardDeviation()),
                        validCount));
            }
            table2.setRows(itemRows);
            tables.add(table2);

            // 3. 项目-总计统计表
            FactorAnalysisOutput.TableData table3 = new FactorAnalysisOutput.TableData();
            table3.setType("reliability_item_total");
            table3.setTitle("项总计统计");
            table3.setHeaders(Arrays.asList("标题", "删除项后的平均值", "删除项后的标度方差", "修正后的项与总计相关性", "删除项后的克隆巴赫Alpha"));
            List<List<Object>> itemTotalRows = new ArrayList<>();
            for (int i = 0; i < itemCount; i++) {
                // 删除第i项后的总分
                List<Double> totalWithoutItem = new ArrayList<>();
                for (int j = 0; j < sampleSize; j++) {
                    double sum = 0.0;
                    for (int k = 0; k < itemCount; k++) {
                        if (k != i)
                            sum += columnData.get(k).get(j);
                    }
                    totalWithoutItem.add(sum);
                }
                DescriptiveStatistics stats = new DescriptiveStatistics();
                totalWithoutItem.forEach(stats::addValue);
                double mean = stats.getMean();
                double var = stats.getVariance();
                // 修正后的相关性：每个项目与去掉该项目后的总分做相关
                double corr = calculateCorrelation(columnData.get(i), totalWithoutItem);
                // 删除项目后的Alpha：每次去掉该项目，重新计算剩余项目的Alpha
                List<List<Double>> remainCols = new ArrayList<>();
                for (int k = 0; k < itemCount; k++) {
                    if (k != i)
                        remainCols.add(columnData.get(k));
                }
                // 重新计算Alpha
                double remainItemVarianceSum = 0.0;
                int remainCount = remainCols.size();
                int remainSample = sampleSize;
                List<Double> remainTotalScores = new ArrayList<>();
                for (int j = 0; j < remainSample; j++) {
                    double sum = 0.0;
                    for (List<Double> col : remainCols) {
                        sum += col.get(j);
                    }
                    remainTotalScores.add(sum);
                }
                DescriptiveStatistics remainTotalStats = new DescriptiveStatistics();
                remainTotalScores.forEach(remainTotalStats::addValue);
                double remainTotalVariance = remainTotalStats.getVariance();
                for (List<Double> col : remainCols) {
                    DescriptiveStatistics s = new DescriptiveStatistics();
                    col.forEach(s::addValue);
                    remainItemVarianceSum += s.getVariance();
                }
                double remainAlpha = (remainCount / (remainCount - 1.0))
                        * (1 - remainItemVarianceSum / remainTotalVariance);
                itemTotalRows.add(Arrays.asList(
                        variableNames.get(i),
                        String.format("%.2f", mean),
                        String.format("%.3f", var),
                        String.format("%.3f", corr),
                        String.format("%.3f", remainAlpha)));
            }
            table3.setRows(itemTotalRows);
            tables.add(table3);

            // 尝试将表格数据放入result对象
            try {
                java.lang.reflect.Method setTables = result.getClass().getMethod("setTables", List.class);
                setTables.invoke(result, tables);
            } catch (Exception e) {
                log.warn("[信度分析] ReliabilityResult未定义tables字段，表格数据未注入: {}", e.getMessage());
            }

            result.setCronbachAlpha(cronbachAlpha);
            result.setStandardizedAlpha(standardizedAlpha);
            result.setTables(tables);

            // === 提取表格列数据为单独变量 ===
            // 项目统计表
            List<String> itemNames = new ArrayList<>();
            List<Double> itemMeans = new ArrayList<>();
            List<Double> itemStds = new ArrayList<>();
            List<Integer> itemCounts = new ArrayList<>();
            // 项目-总计统计表
            List<Double> itemDeletedMeans = new ArrayList<>();
            List<Double> itemDeletedVars = new ArrayList<>();
            List<Double> itemDeletedCorrs = new ArrayList<>();
            for (FactorAnalysisOutput.TableData table : tables) {
                if ("reliability_item".equals(table.getType())) {
                    for (List<Object> row : table.getRows()) {
                        itemNames.add(String.valueOf(row.get(0)));
                        itemMeans.add(parseDouble(row.get(1)));
                        itemStds.add(parseDouble(row.get(2)));
                        itemCounts.add(parseInt(row.get(3)));
                    }
                } else if ("reliability_item_total".equals(table.getType())) {
                    for (List<Object> row : table.getRows()) {
                        itemDeletedMeans.add(parseDouble(row.get(1)));
                        itemDeletedVars.add(parseDouble(row.get(2)));
                        itemDeletedCorrs.add(parseDouble(row.get(3)));
                    }
                }
            }
            result.setItemNames(itemNames);
            result.setItemMeans(itemMeans);
            result.setItemStds(itemStds);
            result.setItemCounts(itemCounts);
            result.setItemDeletedMeans(itemDeletedMeans);
            result.setItemDeletedVars(itemDeletedVars);
            result.setItemDeletedCorrs(itemDeletedCorrs);

            log.info("[信度分析] Cronbach's Alpha分析完成: {}", result);
            saveTableDataToMessage(sessionId, tables);
            return result;
        } catch (Exception e) {
            log.error("[信度分析] Cronbach's Alpha分析异常", e);
            throw new RuntimeException("信度分析异常: " + e.getMessage());
        }
    }

    @Tool(description = "执行探索性因子分析(EFA)，用于降维和发现潜在因子结构")
    public FactorAnalysisOutput performFactorAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "分析列索引列表（从1开始）") List<Integer> columns,
            @ToolParam(description = "因子数量，默认为特征值>1的因子数，如果factors没有指定，就不传入") Integer factors) {
        log.info("[因子分析] 开始探索性因子分析，sessionId={}, columns={}, factors={}", sessionId, columns, factors);
        try {
            // 获取数据
            List<List<Double>> columnData = new ArrayList<>();
            for (Integer col : columns) {
                List<Double> data = getNumericColumnData(sessionId, col - 1);
                if (data.isEmpty()) {
                    throw new RuntimeException("第" + col + "列没有有效的数值数据");
                }
                columnData.add(data);
            }

            int variableCount = columnData.size();
            int sampleSize = columnData.get(0).size();

            // 数据标准化
            List<List<Double>> standardizedData = standardizeData(columnData);

            // 计算相关矩阵
            RealMatrix correlationMatrix = calculateCorrelationMatrix(standardizedData);

            // 计算KMO测度
            double kmoMeasure = calculateKMOMeasure(correlationMatrix);

            // 计算Bartlett球形检验
            double bartlettStatistic = calculateBartlettStatistic(correlationMatrix, sampleSize);
            double bartlettPValue = calculateBartlettPValue(bartlettStatistic, variableCount);
            int bartlettTestDf = variableCount * (variableCount - 1) / 2; // Bartlett检验自由度

            // 构建变量名称 - 从Excel表格中获取实际表头
            List<String> variableNames = new ArrayList<>();
            try {
                // 获取完整的Excel数据以提取表头
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(
                            message.getCompleteExcelData(),
                            new TypeReference<List<List<String>>>() {
                            });

                    // 获取表头行（第一行）
                    if (!excelData.isEmpty()) {
                        List<String> headers = excelData.get(0);
                        for (Integer col : columns) {
                            int colIdx = col - 1; // 转换为0基索引
                            if (colIdx >= 0 && colIdx < headers.size()) {
                                String header = headers.get(colIdx);
                                // 如果表头为空或null，使用默认名称
                                if (header == null || header.trim().isEmpty()) {
                                    variableNames.add("变量" + col);
                                } else {
                                    variableNames.add(header.trim());
                                }
                            } else {
                                variableNames.add("变量" + col);
                            }
                        }
                    } else {
                        // 如果没有表头，使用默认名称
                        for (Integer col : columns) {
                            variableNames.add("变量" + col);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("[因子分析] 获取表头信息失败，使用默认变量名称: {}", e.getMessage());
                // 如果获取表头失败，使用默认名称
                for (Integer col : columns) {
                    variableNames.add("变量" + col);
                }
            }

            // 特征值分解
            EigenDecomposition eigenDecomposition = new EigenDecomposition(correlationMatrix);
            double[] eigenvalues = eigenDecomposition.getRealEigenvalues();

            // 确定因子数量
            int numberOfFactors = factors != null ? factors : countEigenvaluesGreaterThanOne(eigenvalues);
            // 原始相关矩阵的总方差 = 特征值总和
            double totalVariance = Arrays.stream(eigenvalues).sum();
            // 计算方差解释比例 (旋转前)
            Map<Integer, Double> varianceExplainedBefore = new HashMap<>();
            for (int i = 0; i < numberOfFactors; i++) {
                varianceExplainedBefore.put(i, eigenvalues[i] / totalVariance);
            }
            Map<Integer, Double> cumulativeVarianceBefore = calculateCumulativeVariance(varianceExplainedBefore);

            // 构建 FactorAnalysisOutput
            FactorAnalysisOutput output = new FactorAnalysisOutput();
            output.setKmo(kmoMeasure);
            output.setBarelett(bartlettStatistic);
            output.setDf((double) bartlettTestDf);
            output.setPValue(bartlettPValue);
            output.setFactors(numberOfFactors);

            // 填充旋转前数据
            output.setTzgBefore(Arrays.stream(eigenvalues).boxed().limit(numberOfFactors).collect(Collectors.toList()));
            output.setFcBefore(new ArrayList<>(varianceExplainedBefore.values()).subList(0, numberOfFactors));
            output.setLjfcBefore(new ArrayList<>(cumulativeVarianceBefore.values()).subList(0, numberOfFactors));

            // 1. 提取未旋转的因子载荷
            RealMatrix eigenvectors = eigenDecomposition.getV();
            double[] eigenvaluesArr = eigenDecomposition.getRealEigenvalues();
            double[][] loadings = new double[variableCount][numberOfFactors];
            for (int i = 0; i < variableCount; i++) {
                for (int j = 0; j < numberOfFactors; j++) {
                    loadings[i][j] = eigenvectors.getEntry(i, j) * Math.sqrt(eigenvaluesArr[j]);
                }
            }

            // 2. 进行Varimax旋转 - 增加迭代次数和容差
            double[][] rotatedLoadings = varimax(loadings, 25, 1e-5);

            // 3. 调整因子载荷符号（确保每列的和为正）
            for (int j = 0; j < numberOfFactors; j++) {
                double sum = 0.0;
                for (int i = 0; i < variableCount; i++) {
                    sum += rotatedLoadings[i][j];
                }
                if (sum < 0) {
                    for (int i = 0; i < variableCount; i++) {
                        rotatedLoadings[i][j] = -rotatedLoadings[i][j];
                    }
                }
            }

            // 4. 计算旋转后特征根（每列平方和）
            double[] rotatedEigenvalues = new double[numberOfFactors];
            for (int j = 0; j < numberOfFactors; j++) {
                double sum = 0.0;
                for (int i = 0; i < variableCount; i++) {
                    sum += Math.pow(rotatedLoadings[i][j], 2);
                }
                rotatedEigenvalues[j] = sum;
            }

            // 5. 按特征根大小降序排序
            List<Integer> sortedIndices = IntStream.range(0, numberOfFactors)
                    .boxed()
                    .sorted(Comparator.comparingDouble(i -> -rotatedEigenvalues[i]))
                    .collect(Collectors.toList());

            // 6. 重新排序载荷矩阵和特征根
            double[][] sortedRotatedLoadings = new double[variableCount][numberOfFactors];
            double[] sortedRotatedEigenvalues = new double[numberOfFactors];
            for (int j = 0; j < numberOfFactors; j++) {
                int origIndex = sortedIndices.get(j);
                sortedRotatedEigenvalues[j] = rotatedEigenvalues[origIndex];
                for (int i = 0; i < variableCount; i++) {
                    sortedRotatedLoadings[i][j] = rotatedLoadings[i][origIndex];
                }
            }

            // 7. 计算旋转后方差解释率和累积方差解释率
            List<Double> rotatedVarianceExplained = new ArrayList<>();
            for (double eigenvalue : sortedRotatedEigenvalues) {
                rotatedVarianceExplained.add(eigenvalue / totalVariance);
            }

            // 计算旋转后累积方差解释率
            List<Double> rotatedCumulativeVariance = new ArrayList<>();
            double cumulative = 0.0;
            for (Double var : rotatedVarianceExplained) {
                cumulative += var;
                rotatedCumulativeVariance.add(cumulative);
            }

            // 8. 设置旋转后数据
            output.setTzgAfter(Arrays.stream(sortedRotatedEigenvalues).boxed().collect(Collectors.toList()));
            output.setFcAfter(rotatedVarianceExplained);
            output.setLjfcAfter(rotatedCumulativeVariance);

            // 9. 构建因子分析项
            List<FactorAnalysisOutput.FactorAnalysisItem> items = new ArrayList<>();
            for (int i = 0; i < variableCount; i++) {
                // 修复：正确创建FactorAnalysisItem实例
                FactorAnalysisOutput.FactorAnalysisItem item = new FactorAnalysisOutput.FactorAnalysisItem();
                item.setTitle(variableNames.get(i));

                List<Double> currentItemLoadings = new ArrayList<>();
                double communality = 0.0;
                for (int j = 0; j < numberOfFactors; j++) {
                    double loading = sortedRotatedLoadings[i][j];
                    currentItemLoadings.add(loading);
                    communality += loading * loading;
                }
                // 确保共同度<=1 (标准化数据)
                communality = Math.min(communality, 1.0);
                item.setFators(currentItemLoadings);
                item.setHValue(communality);
                items.add(item);
            }
            output.setItems(items);

            // 在返回结果前添加表格数据
            List<FactorAnalysisOutput.TableData> tables = new ArrayList<>();

            // 1. KMO和Bartlett检验表
            FactorAnalysisOutput.TableData kmoTable = new FactorAnalysisOutput.TableData();
            kmoTable.setType("factor_analysis_kmo");
            kmoTable.setTitle("KMO和Bartlett检验");
            kmoTable.setHeaders(Arrays.asList("检验类型", "值"));
            kmoTable.setRows(Arrays.asList(
                    Arrays.asList("KMO值", String.format("%.3f", output.getKmo())),
                    Arrays.asList("巴特球形值", String.format("%.3f", output.getBarelett())),
                    Arrays.asList("df", String.format("%.1f", output.getDf())),
                    Arrays.asList("p值", String.format("%.3f", output.getPValue()))));
            tables.add(kmoTable);

            // 2. 生成合并后的表格（载荷+方差解释率）
            FactorAnalysisOutput.TableData mergedTable = new FactorAnalysisOutput.TableData();
            mergedTable.setType("factor_analysis_merged");
            mergedTable.setTitle("因子载荷与方差解释率");
            List<String> mergedHeaders = new ArrayList<>();
            mergedHeaders.add("项目");
            for (int i = 1; i <= output.getFactors(); i++) {
                mergedHeaders.add("因子" + i);
            }
            mergedHeaders.add("共同度");
            mergedTable.setHeaders(mergedHeaders);

            List<List<Object>> mergedRows = new ArrayList<>();
            List<List<FactorAnalysisOutput.CellStyle>> mergedCellStyles = new ArrayList<>();
            // 先添加每个项目的载荷和共同度 - 使用排序后的载荷
            for (int i = 0; i < variableCount; i++) {
                List<Object> row = new ArrayList<>();
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                row.add(variableNames.get(i));
                styleRow.add(newCellStyle(null, null));
                double maxVal = Double.NEGATIVE_INFINITY;
                int maxIdx = -1;
                double communality = 0.0;
                for (int j = 0; j < numberOfFactors; j++) {
                    double loading = sortedRotatedLoadings[i][j];
                    row.add(String.format("%.3f", loading));
                    communality += loading * loading;
                    // 记录最大值下标
                    if (loading > maxVal) {
                        maxVal = loading;
                        maxIdx = j;
                    }
                    styleRow.add(newCellStyle(null, null));
                }
                row.add(String.format("%.3f", communality));
                styleRow.add(newCellStyle(null, null));
                // 设置最大因子载荷为红色
                if (maxIdx >= 0) {
                    styleRow.set(1 + maxIdx, newCellStyle(null, "#d62728"));
                }
                mergedRows.add(row);
                mergedCellStyles.add(styleRow);
            }
            // 添加空行分隔
            mergedRows.add(new ArrayList<>());
            mergedCellStyles.add(new ArrayList<>());
            // 添加特征根值和方差解释率等统计行
            List<Object> eigenvalueBeforeRow = new ArrayList<>();
            eigenvalueBeforeRow.add("特征根值(旋转前)");
            for (int i = 0; i < output.getFactors(); i++) {
                eigenvalueBeforeRow.add(String.format("%.2f", output.getTzgBefore().get(i)));
            }
            eigenvalueBeforeRow.add("-");
            mergedRows.add(eigenvalueBeforeRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow1 = new ArrayList<>();
            for (int i = 0; i < eigenvalueBeforeRow.size(); i++)
                statStyleRow1.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow1);

            List<Object> varianceBeforeRow = new ArrayList<>();
            varianceBeforeRow.add("方差解释率%(旋转前)");
            for (int i = 0; i < output.getFactors(); i++) {
                varianceBeforeRow.add(String.format("%.2f%%", output.getFcBefore().get(i) * 100));
            }
            varianceBeforeRow.add("-");
            mergedRows.add(varianceBeforeRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow2 = new ArrayList<>();
            for (int i = 0; i < varianceBeforeRow.size(); i++)
                statStyleRow2.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow2);

            List<Object> cumulativeBeforeRow = new ArrayList<>();
            cumulativeBeforeRow.add("累积方差解释率%(旋转前)");
            for (int i = 0; i < output.getFactors(); i++) {
                cumulativeBeforeRow.add(String.format("%.2f%%", output.getLjfcBefore().get(i) * 100));
            }
            cumulativeBeforeRow.add("-");
            mergedRows.add(cumulativeBeforeRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow3 = new ArrayList<>();
            for (int i = 0; i < cumulativeBeforeRow.size(); i++)
                statStyleRow3.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow3);

            List<Object> eigenvalueAfterRow = new ArrayList<>();
            eigenvalueAfterRow.add("特征根值(旋转后)");
            for (int i = 0; i < output.getFactors(); i++) {
                eigenvalueAfterRow.add(String.format("%.2f", sortedRotatedEigenvalues[i])); // 使用排序后的特征根
            }
            eigenvalueAfterRow.add("-");
            mergedRows.add(eigenvalueAfterRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow4 = new ArrayList<>();
            for (int i = 0; i < eigenvalueAfterRow.size(); i++)
                statStyleRow4.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow4);

            List<Object> varianceAfterRow = new ArrayList<>();
            varianceAfterRow.add("方差解释率%(旋转后)");
            for (int i = 0; i < output.getFactors(); i++) {
                varianceAfterRow.add(String.format("%.2f%%", rotatedVarianceExplained.get(i) * 100));
            }
            varianceAfterRow.add("-");
            mergedRows.add(varianceAfterRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow5 = new ArrayList<>();
            for (int i = 0; i < varianceAfterRow.size(); i++)
                statStyleRow5.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow5);

            List<Object> cumulativeAfterRow = new ArrayList<>();
            cumulativeAfterRow.add("累积方差解释率%(旋转后)");
            for (int i = 0; i < output.getFactors(); i++) {
                cumulativeAfterRow.add(String.format("%.2f%%", rotatedCumulativeVariance.get(i) * 100));
            }
            cumulativeAfterRow.add("-");
            mergedRows.add(cumulativeAfterRow);
            List<FactorAnalysisOutput.CellStyle> statStyleRow6 = new ArrayList<>();
            for (int i = 0; i < cumulativeAfterRow.size(); i++)
                statStyleRow6.add(newCellStyle(null, null));
            mergedCellStyles.add(statStyleRow6);

            mergedTable.setRows(mergedRows);
            mergedTable.setCellStyles(mergedCellStyles);

            tables.clear();
            tables.add(kmoTable);
            tables.add(mergedTable);
            output.setTables(tables);

            // 这里先生成一个ai_chat_message数据入库
            saveTableDataToMessage(sessionId, tables);
            return output;
        } catch (Exception e) {
            log.error("[因子分析] 探索性因子分析异常", e);
            throw new RuntimeException("因子分析异常: " + e.getMessage());
        }
    }

    // 辅助方法
    private double calculateCorrelation(List<Double> x, List<Double> y) {
        PearsonsCorrelation correlation = new PearsonsCorrelation();
        return correlation.correlation(
                x.stream().mapToDouble(Double::doubleValue).toArray(),
                y.stream().mapToDouble(Double::doubleValue).toArray());
    }

    private double calculateStandardizedAlpha(List<List<Double>> columnData) {
        // 计算标准化Alpha的简化实现
        int itemCount = columnData.size();
        double averageCorrelation = calculateAverageInterItemCorrelation(columnData);
        return (itemCount * averageCorrelation) / (1 + (itemCount - 1) * averageCorrelation);
    }

    private double calculateAverageInterItemCorrelation(List<List<Double>> columnData) {
        int itemCount = columnData.size();
        double totalCorrelation = 0.0;
        int correlationCount = 0;

        for (int i = 0; i < itemCount; i++) {
            for (int j = i + 1; j < itemCount; j++) {
                totalCorrelation += calculateCorrelation(columnData.get(i), columnData.get(j));
                correlationCount++;
            }
        }

        return correlationCount > 0 ? totalCorrelation / correlationCount : 0.0;
    }

    private String determineReliabilityLevel(double alpha) {
        if (alpha >= 0.9)
            return "优秀";
        else if (alpha >= 0.8)
            return "良好";
        else if (alpha >= 0.7)
            return "可接受";
        else if (alpha >= 0.6)
            return "可疑";
        else
            return "不可接受";
    }

    private double calculateCorrelationPValue(double correlation, int sampleSize) {
        double t = correlation * Math.sqrt((sampleSize - 2) / (1 - correlation * correlation));
        TDistribution tDist = new TDistribution(sampleSize - 2);
        return 2 * (1 - tDist.cumulativeProbability(Math.abs(t)));
    }

    private List<List<Double>> standardizeData(List<List<Double>> data) {
        return data.stream().map(column -> {
            double mean = column.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            final double std = Math
                    .max(Math.sqrt(column.stream().mapToDouble(v -> Math.pow(v - mean, 2)).sum() / (column.size() - 1)),
                            1e-10);
            return column.stream().map(v -> (v - mean) / std).collect(Collectors.toList());
        }).collect(Collectors.toList());
    }

    private RealMatrix calculateCorrelationMatrix(List<List<Double>> standardizedData) {
        int nVars = standardizedData.size();
        int nObs = standardizedData.get(0).size();

        double[][] dataArray = new double[nObs][nVars];
        for (int i = 0; i < nObs; i++) {
            for (int j = 0; j < nVars; j++) {
                dataArray[i][j] = standardizedData.get(j).get(i);
            }
        }

        return new PearsonsCorrelation().computeCorrelationMatrix(dataArray);
    }

    private double calculateKMOMeasure(RealMatrix correlationMatrix) {
        int n = correlationMatrix.getRowDimension();
        RealMatrix inverseCorrelationMatrix;
        try {
            inverseCorrelationMatrix = new LUDecomposition(correlationMatrix).getSolver().getInverse();
        } catch (SingularMatrixException e) {
            log.warn("无法计算相关矩阵的逆矩阵，可能存在共线性问题，KMO返回0.0");
            return 0.0; // 如果矩阵是奇异的，无法计算逆矩阵
        }

        double sumOfSquaredCorrelations = 0.0;
        double sumOfSquaredPartialCorrelations = 0.0;

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i == j)
                    continue;

                // 计算简单相关系数的平方和
                sumOfSquaredCorrelations += Math.pow(correlationMatrix.getEntry(i, j), 2);

                // 计算偏相关系数的平方和
                // u_ij = -R_inv,ij / sqrt(R_inv,ii * R_inv,jj)
                double partialCorrelation = -inverseCorrelationMatrix.getEntry(i, j) /
                        Math.sqrt(inverseCorrelationMatrix.getEntry(i, i) * inverseCorrelationMatrix.getEntry(j, j));
                sumOfSquaredPartialCorrelations += Math.pow(partialCorrelation, 2);
            }
        }

        // KMO = (sum_r^2) / (sum_r^2 + sum_u^2)
        double numerator = sumOfSquaredCorrelations;
        double denominator = sumOfSquaredCorrelations + sumOfSquaredPartialCorrelations;

        if (denominator == 0) {
            return 0.0;
        } else {
            return numerator / denominator;
        }
    }

    private double calculateBartlettStatistic(RealMatrix correlationMatrix, int sampleSize) {
        int variableCount = correlationMatrix.getRowDimension();
        double determinant = new LUDecomposition(correlationMatrix).getDeterminant();
        return -(sampleSize - 1 - (2 * variableCount + 5) / 6.0) * Math.log(determinant);
    }

    private double calculateBartlettPValue(double statistic, int variableCount) {
        // 简化实现，实际应使用卡方分布
        return 0.001; // 占位符
    }

    private int countEigenvaluesGreaterThanOne(double[] eigenvalues) {
        return (int) Arrays.stream(eigenvalues).filter(e -> e > 1.0).count();
    }

    private Map<Integer, Double> calculateCumulativeVariance(Map<Integer, Double> varianceExplained) {
        Map<Integer, Double> cumulativeVariance = new HashMap<>();
        double cumulative = 0.0;

        for (Map.Entry<Integer, Double> entry : varianceExplained.entrySet()) {
            cumulative += entry.getValue();
            cumulativeVariance.put(entry.getKey(), cumulative);
        }

        return cumulativeVariance;
    }

    // Varimax旋转实现 - 修复收敛问题
    // 修改后的Varimax旋转实现
    // 修改1: 添加Kaiser归一化处理
    private double[][] varimax(double[][] loadings, int maxIter, double epsilon) {
        int nVars = loadings.length;
        int nFactors = loadings[0].length;

        // 计算初始共同度
        double[] h = new double[nVars];
        for (int i = 0; i < nVars; i++) {
            double sumSq = 0.0;
            for (int j = 0; j < nFactors; j++) {
                sumSq += loadings[i][j] * loadings[i][j];
            }
            h[i] = sumSq;
        }

        // Kaiser归一化
        double[][] normalized = new double[nVars][nFactors];
        for (int i = 0; i < nVars; i++) {
            double sqrtH = Math.sqrt(h[i]);
            for (int j = 0; j < nFactors; j++) {
                normalized[i][j] = (sqrtH > 1e-10) ? loadings[i][j] / sqrtH : 0.0;
            }
        }

        double[][] rotated = Arrays.stream(normalized).map(double[]::clone).toArray(double[][]::new);

        // 修改2: 使用标准Varimax角度计算公式
        int iter = 0;
        double diff;
        do {
            diff = 0.0;
            for (int j = 0; j < nFactors - 1; j++) {
                for (int k = j + 1; k < nFactors; k++) {
                    double A = 0.0, B = 0.0, C = 0.0, D = 0.0;

                    for (int i = 0; i < nVars; i++) {
                        double a = rotated[i][j];
                        double b = rotated[i][k];
                        double u = a * a - b * b;
                        double v = 2 * a * b;
                        A += u;
                        B += v;
                        C += u * u - v * v;
                        D += 2 * u * v;
                    }

                    // 标准Varimax角度计算公式
                    double numerator = D - 2 * A * B / nVars;
                    double denominator = C - (A * A - B * B) / nVars;
                    double angle = (Math.abs(denominator) < 1e-10) ? 0.0 : 0.25 * Math.atan2(numerator, denominator);

                    double cos = Math.cos(angle);
                    double sin = Math.sin(angle);

                    double maxChange = 0.0;
                    for (int i = 0; i < nVars; i++) {
                        double a = rotated[i][j];
                        double b = rotated[i][k];
                        double newA = a * cos + b * sin;
                        double newB = -a * sin + b * cos;

                        maxChange = Math.max(maxChange, Math.abs(newA - a));
                        maxChange = Math.max(maxChange, Math.abs(newB - b));

                        rotated[i][j] = newA;
                        rotated[i][k] = newB;
                    }
                    diff = Math.max(diff, maxChange);
                }
            }
            iter++;
        } while (diff > epsilon && iter < maxIter);

        // 反归一化
        for (int i = 0; i < nVars; i++) {
            double sqrtH = Math.sqrt(h[i]);
            for (int j = 0; j < nFactors; j++) {
                rotated[i][j] = (sqrtH > 1e-10) ? rotated[i][j] * sqrtH : 0.0;
            }
        }

        log.info("Varimax旋转在 {} 次迭代后收敛，最大变化={}", iter, diff);
        return rotated;
    }

    // 辅助方法：矩阵乘法
    private double[][] matrixMultiply(double[][] a, double[][] b) {
        int m = a.length;
        int n = b[0].length;
        int p = b.length;

        double[][] c = new double[m][n];
        for (int i = 0; i < m; i++) {
            for (int j = 0; j < n; j++) {
                for (int k = 0; k < p; k++) {
                    c[i][j] += a[i][k] * b[k][j];
                }
            }
        }
        return c;
    }

    // 表格数据入库通用方法
    private void saveTableDataToMessage(String sessionId, List<FactorAnalysisOutput.TableData> tables) {
        try {
            AiChatMessage lastMsg = messageMapper.findLatestWaitingFillBySessionId(sessionId);
            ObjectMapper objectMapper = new ObjectMapper();
            if (lastMsg != null) {
                // 如果存在等待AI填充的消息，则将表格数据在原来的基础上追加，要注意表格数据格式,最外面有一个[]，追加的时候要先去掉[]，追加完再添加[]
                String tableData = lastMsg.getTableData();
                StringBuilder sb = new StringBuilder();
                if (tableData != null && tableData.startsWith("[") && tableData.endsWith("]")) {
                    String inner = tableData.substring(1, tableData.length() - 1).trim();
                    if (!inner.isEmpty()) {
                        sb.append(inner);
                    }
                }
                for (FactorAnalysisOutput.TableData table : tables) {
                    if (sb.length() > 0)
                        sb.append(",");
                    sb.append(objectMapper.writeValueAsString(table));
                }
                String newTableData = "[" + sb.toString() + "]";
                lastMsg.setTableData(newTableData);
                messageMapper.update(lastMsg);
            } else {
                AiChatMessage aiMessageChunk = new AiChatMessage();
                aiMessageChunk.setSessionId(sessionId);
                aiMessageChunk.setRole("assistant");
                aiMessageChunk.setContent("等待AI填充");
                aiMessageChunk.onCreate();
                aiMessageChunk.setTableData(objectMapper.writeValueAsString(tables));
                messageMapper.insert(aiMessageChunk);
            }
        } catch (Exception e) {
            log.error("[表格数据入库] 失败: {}", e.getMessage(), e);
        }
    }

    @Tool(description = "计算一组数值的均值")
    public Double calculateMean(@ToolParam(description = "数值数组") List<Double> values) {
        if (values == null || values.isEmpty())
            return null;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        return stats.getMean();
    }

    @Tool(description = "计算一组数值的标准差")
    public Double calculateStd(@ToolParam(description = "数值数组") List<Double> values) {
        if (values == null || values.isEmpty())
            return null;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        return stats.getStandardDeviation();
    }

    @Tool(description = "计算一组数值的最小值")
    public Double calculateMin(@ToolParam(description = "数值数组") List<Double> values) {
        if (values == null || values.isEmpty())
            return null;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        return stats.getMin();
    }

    @Tool(description = "计算一组数值的最大值")
    public Double calculateMax(@ToolParam(description = "数值数组") List<Double> values) {
        if (values == null || values.isEmpty())
            return null;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        return stats.getMax();
    }

    @Tool(description = "计算一组数值的指定分位数")
    public Double calculatePercentile(@ToolParam(description = "数值数组") List<Double> values,
            @ToolParam(description = "分位数0-100") Double p) {
        if (values == null || values.isEmpty() || p == null)
            return null;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        return stats.getPercentile(p);
    }

    @Tool(description = "分析一组数值，返回最小值、最大值、均值、标准差、分位数等")
    public Map<String, Object> analyzeArray(@ToolParam(description = "数值数组") List<Double> values) {
        Map<String, Object> result = new HashMap<>();
        if (values == null || values.isEmpty())
            return result;
        DescriptiveStatistics stats = new DescriptiveStatistics();
        values.forEach(stats::addValue);
        result.put("min", stats.getMin());
        result.put("max", stats.getMax());
        result.put("mean", stats.getMean());
        result.put("std", stats.getStandardDeviation());
        result.put("p25", stats.getPercentile(25));
        result.put("p50", stats.getPercentile(50));
        result.put("p75", stats.getPercentile(75));
        return result;
    }

    // 工具方法
    private Double parseDouble(Object obj) {
        if (obj == null)
            return null;
        try {
            return Double.parseDouble(obj.toString());
        } catch (Exception e) {
            return null;
        }
    }

    private Integer parseInt(Object obj) {
        if (obj == null)
            return null;
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通用频数统计分析，支持单选、多选、排序题、矩阵单选和矩阵多选
     * 
     * @param sessionId    会话ID
     * @param questionNums 题号列表（从1开始），如果需要分析所有适用的题目，请传入[-1]
     * @return 统计表格数据列表
     */
    @Tool(description = "频数统计分析，仅支持题目type为3, 4, 5, 7, 6single, 6multiple, 11，生成频数统计表格。可一次性接受多个题号进行分析。")
    public List<FactorAnalysisOutput.TableData> frequencyStatistics(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "需要分析的题号列表（从1开始）。如果需要分析所有适用的题目，请传入一个包含-1的列表，例如[-1]") List<Integer> questionNums) {
        log.info("[frequencyStatistics] 收到题号列表: {}", questionNums);

        if (questionNums == null || questionNums.isEmpty()) {
            throw new RuntimeException("题号列表不能为空");
        }

        final Set<String> APPLICABLE_TYPES = new HashSet<>(
                Arrays.asList("3", "4", "5", "7", "6single", "6multiple", "11"));
        List<Integer> finalQuestionNums = new ArrayList<>();

        if (questionNums.contains(-1)) {
            log.info("[frequencyStatistics] 检测到-1，将分析所有适用题目");
            WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
            if (surveyData == null || surveyData.getJsonData() == null) {
                throw new RuntimeException("未找到会话 " + sessionId + " 的问卷数据，无法执行全部分析");
            }
            surveyData.getJsonData().stream()
                    .filter(data -> APPLICABLE_TYPES.contains(data.getType()))
                    .map(SurveyData::getNumId)
                    .forEach(finalQuestionNums::add);
            log.info("[frequencyStatistics] 识别出所有适用题号: {}", finalQuestionNums);
        } else {
            finalQuestionNums.addAll(questionNums);
        }

        if (finalQuestionNums.isEmpty()) {
            log.warn("[frequencyStatistics] 未找到任何可以分析的题目，会话ID: {}", sessionId);
            return Collections.emptyList();
        }

        List<FactorAnalysisOutput.TableData> allResults = new ArrayList<>();

        try {
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message == null || message.getCompleteExcelData() == null) {
                throw new RuntimeException("未找到对应的Excel数据");
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                    new TypeReference<List<List<String>>>() {
                    });
            if (excelData.size() < 2)
                throw new RuntimeException("数据不足");
            List<String> header = excelData.get(0);
            List<List<String>> dataRows = excelData.subList(1, excelData.size());

            for (Integer questionNum : finalQuestionNums) {
                try {
                    log.info("[frequencyStatistics] 正在处理题号: {}", questionNum);

                    SurveyStructure targetStruct = getSurveyStructureFromDatabase(sessionId, questionNum);
                    if (targetStruct == null) {
                        throw new RuntimeException("未找到题号为 " + questionNum + " 的题目结构信息");
                    }

                    String type = targetStruct.getType();
                    List<Integer> columnIndices = targetStruct.getColIndices();
                    FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();

                    if ("3".equals(type) || "5".equals(type) || "7".equals(type)) {
                        // 单选题
                        int col = columnIndices.get(0) - 1;
                        Map<String, Long> freq = dataRows.stream().collect(Collectors.groupingBy(
                                row -> (col < row.size() && row.get(col) != null && !row.get(col).isEmpty())
                                        ? row.get(col)
                                        : "缺失",
                                Collectors.counting()));
                        long total = dataRows.size();
                        List<List<Object>> rows = new ArrayList<>();

                        // ==== 选项编号映射为文本 ====
                        Map<String, String> optionLabelMap = new HashMap<>();
                        List<String> optionOrder = new ArrayList<>();
                        String questionTitle = "";
                        String questionNumStr = "";

                        if (targetStruct != null) {
                            List<String> options = targetStruct.getOptions();
                            if (options != null) {
                                for (int i = 0; i < options.size(); i++) {
                                    String key = String.valueOf(i + 1);
                                    optionLabelMap.put(key, options.get(i));
                                    optionOrder.add(key);
                                }
                            }
                            if (targetStruct.getTitle() != null) {
                                questionTitle = targetStruct.getTitle();
                            }
                            if (targetStruct.getNumId() != null) {
                                questionNumStr = targetStruct.getNumId().toString();
                            }
                        }
                        // =========================

                        // 按选项顺序输出
                        for (String key : optionOrder) {
                            String label = optionLabelMap.getOrDefault(key, key);
                            long count = freq.getOrDefault(key, 0L);
                            double percent = total == 0 ? 0 : count * 100.0 / total;
                            rows.add(Arrays.asList(label, count, String.format("%.2f%%", percent)));
                        }
                        // 补充缺失项（如有）
                        if (freq.containsKey("缺失")) {
                            long count = freq.get("缺失");
                            double percent = total == 0 ? 0 : count * 100.0 / total;
                            rows.add(Arrays.asList("缺失", count, String.format("%.2f%%", percent)));
                        }
                        // 增加小计行
                        long validRespondents = total - freq.getOrDefault("缺失", 0L);
                        rows.add(Arrays.asList("小计", validRespondents, "-"));

                        table.setType("single_choice_statistics");
                        table.setTitle(questionNumStr + "." + questionTitle + "[单选题]");
                        table.setHeaders(Arrays.asList("选项", "频数", "百分比"));
                        table.setRows(rows);
                    } else if ("4".equals(type)) {
                        // 多选题
                        int n = columnIndices.size();
                        long total = dataRows.size();
                        List<String> optionNames = null;
                        String questionTitle = "";
                        String questionNumStr = "";
                        if (targetStruct != null) {
                            optionNames = targetStruct.getOptions();
                            questionTitle = targetStruct.getTitle() != null ? targetStruct.getTitle() : "";
                            if (targetStruct.getNumId() != null) {
                                questionNumStr = targetStruct.getNumId().toString();
                            }
                        }

                        if (optionNames == null) {
                            // 兜底：用表头
                            optionNames = columnIndices.stream()
                                    .map(idx -> idx - 1 < header.size() ? header.get(idx - 1) : ("选项" + idx))
                                    .collect(Collectors.toList());
                        }
                        long[] freq = new long[n];
                        long[] caseFreq = new long[n];
                        for (List<String> row : dataRows) {
                            for (int i = 0; i < n; i++) {
                                int col = columnIndices.get(i) - 1;
                                if (col < row.size() && "1".equals(row.get(col))) {
                                    freq[i]++;
                                }
                            }
                        }
                        // 个案百分比：有多少人选了该项
                        for (int i = 0; i < n; i++) {
                            int col = columnIndices.get(i) - 1;
                            caseFreq[i] = (int) dataRows.stream()
                                    .filter(row -> col < row.size() && "1".equals(row.get(col))).count();
                        }
                        List<List<Object>> rows = new ArrayList<>();
                        long freqSum = Arrays.stream(freq).sum();
                        for (int i = 0; i < n; i++) {
                            double percent = freqSum == 0 ? 0 : freq[i] * 100.0 / freqSum;
                            double casePercent = total == 0 ? 0 : caseFreq[i] * 100.0 / total;
                            rows.add(Arrays.asList(optionNames.get(i), freq[i], String.format("%.1f%%", percent),
                                    String.format("%.1f%%", casePercent)));
                        }
                        // 增加小计行
                        long validMultiRespondents = dataRows.stream()
                                .filter(r -> columnIndices.stream().anyMatch(idx -> {
                                    int c = idx - 1;
                                    return c < r.size() && "1".equals(r.get(c));
                                })).count();
                        rows.add(Arrays.asList("小计", validMultiRespondents, "-", "-"));

                        table.setType("multiple_choice_statistics");
                        table.setTitle(questionNumStr + "." + questionTitle + "[多选题]");
                        table.setHeaders(Arrays.asList("选项", "频数", "百分比", "个案百分比"));
                        table.setRows(rows);
                    } else if ("11".equals(type)) {
                        // 排序题 (新逻辑：列是选项，值是排名)

                        // 1. 获取选项名称和数量
                        List<String> optionNames = new ArrayList<>();
                        String questionTitle = "";
                        String questionNumStr = "";
                        if (targetStruct != null) {
                            if (targetStruct.getOptions() != null && !targetStruct.getOptions().isEmpty()) {
                                optionNames.addAll(targetStruct.getOptions());
                            }
                            questionTitle = targetStruct.getTitle() != null ? targetStruct.getTitle() : "";
                            questionNumStr = targetStruct.getNumId() != null ? targetStruct.getNumId().toString() : "";
                        }

                        // 如果从结构中未获取到选项，则从Excel表头推断
                        if (optionNames.isEmpty()) {
                            for (Integer colIndex : columnIndices) {
                                if (colIndex > 0 && colIndex <= header.size()) {
                                    optionNames.add(header.get(colIndex - 1));
                                } else {
                                    optionNames.add("未知选项" + colIndex);
                                }
                            }
                        }

                        int numOptions = optionNames.size();
                        int numRanks = numOptions; // 排名数量通常等于选项数量
                        if (numOptions == 0) {
                            throw new RuntimeException("排序题 '" + questionTitle + "' 没有找到任何选项");
                        }

                        // 2. 初始化统计矩阵和计算有效回答数
                        long[][] counts = new long[numOptions][numRanks];
                        Set<Integer> validRespondentRowIndices = new HashSet<>();

                        // 遍历每个 respondent
                        for (int rowIndex = 0; rowIndex < dataRows.size(); rowIndex++) {
                            List<String> row = dataRows.get(rowIndex);
                            boolean isThisRowValid = false;

                            // 遍历每个选项（即每列）
                            for (int optionIndex = 0; optionIndex < numOptions; optionIndex++) {
                                if (optionIndex >= columnIndices.size())
                                    continue; // 防止索引越界
                                int col = columnIndices.get(optionIndex) - 1;

                                if (col >= 0 && col < row.size() && row.get(col) != null && !row.get(col).isEmpty()) {
                                    try {
                                        int rank = Integer.parseInt(row.get(col).trim());
                                        int rankIndex = rank - 1; // 排名从1开始，角标从0开始

                                        if (rankIndex >= 0 && rankIndex < numRanks) {
                                            counts[optionIndex][rankIndex]++;
                                            isThisRowValid = true;
                                        }
                                    } catch (NumberFormatException e) {
                                        log.warn("[frequencyStatistics] 排序题数据解析失败: {}", row.get(col));
                                    }
                                }
                            }
                            if (isThisRowValid) {
                                validRespondentRowIndices.add(rowIndex);
                            }
                        }
                        long validRespondents = validRespondentRowIndices.size();

                        // 3. 计算每个选项的统计数据
                        List<Map<String, Object>> results = new ArrayList<>();
                        for (int optionIndex = 0; optionIndex < numOptions; optionIndex++) {
                            long weightedSum = 0;
                            for (int rankIndex = 0; rankIndex < numRanks; rankIndex++) {
                                int weight = numRanks - rankIndex;
                                weightedSum += counts[optionIndex][rankIndex] * weight;
                            }
                            double comprehensiveScore = (validRespondents > 0) ? (double) weightedSum / validRespondents
                                    : 0.0;

                            Map<String, Object> optionResult = new LinkedHashMap<>();
                            optionResult.put("optionName", optionNames.get(optionIndex));
                            optionResult.put("comprehensiveScore", comprehensiveScore);
                            optionResult.put("counts", counts[optionIndex]);
                            optionResult.put("totalRanked", Arrays.stream(counts[optionIndex]).sum());
                            results.add(optionResult);
                        }

                        // 4. 按综合得分降序排序, 只保留被选择过的选项
                        List<Map<String, Object>> sortedResults = results.stream()
                                .filter(m -> (long) m.get("totalRanked") > 0)
                                .sorted((m1, m2) -> ((Double) m2.get("comprehensiveScore"))
                                        .compareTo((Double) m1.get("comprehensiveScore")))
                                .collect(Collectors.toList());

                        // 5. 构建表头
                        List<String> headers = new ArrayList<>();
                        headers.add("选项");
                        headers.add("综合得分");
                        for (int i = 1; i <= numRanks; i++) {
                            headers.add("第" + i + "位");
                        }
                        headers.add("小计");
                        table.setHeaders(headers);

                        // 6. 构建表格行
                        List<List<Object>> tableRows = new ArrayList<>();
                        for (Map<String, Object> result : sortedResults) {
                            List<Object> row = new ArrayList<>();
                            row.add(result.get("optionName"));
                            row.add(String.format("%.2f", (Double) result.get("comprehensiveScore")));

                            long[] rankCounts = (long[]) result.get("counts");
                            for (int rankIndex = 0; rankIndex < numRanks; rankIndex++) {
                                long count = rankCounts[rankIndex];
                                double percent = (validRespondents > 0) ? count * 100.0 / validRespondents : 0.0;
                                row.add(String.format("%d (%.2f%%)", count, percent));
                            }

                            row.add(validRespondents);
                            tableRows.add(row);
                        }

                        table.setType("rank_statistics");
                        table.setTitle(questionNumStr + "." + questionTitle + "[排序题]");
                        table.setRows(tableRows);
                    } else if ("6single".equals(type)) {
                        // 矩阵单选题（新表格结构）
                        table.setType("matrix_single_statistics");
                        table.setTitle(targetStruct.getNumId() + "." + targetStruct.getTitle() + "[矩阵单选题]");

                        List<String> headers = new ArrayList<>();
                        headers.add("题目\\选项");
                        // 获取选项作为表头 - 优先使用第一个子问题的选项
                        List<String> optionHeaders = null;
                        if (targetStruct.getSubQuestions() != null && !targetStruct.getSubQuestions().isEmpty()) {
                            SurveyData.SubQuestion firstSubQ = targetStruct.getSubQuestions().get(0);
                            if (firstSubQ != null && firstSubQ.getOptions() != null
                                    && !firstSubQ.getOptions().isEmpty()) {
                                optionHeaders = firstSubQ.getOptions();
                            }
                        }
                        if (optionHeaders == null && targetStruct.getOptions() != null
                                && !targetStruct.getOptions().isEmpty()) {
                            optionHeaders = targetStruct.getOptions();
                        }
                        if (optionHeaders == null) {
                            // 动态推断选项数
                            Set<String> uniqueValues = new HashSet<>();
                            for (List<String> row : dataRows) {
                                for (Integer colIndex : columnIndices) {
                                    int col = colIndex - 1;
                                    if (col < row.size() && row.get(col) != null && !row.get(col).isEmpty()) {
                                        String value = row.get(col).trim();
                                        if (value.matches("\\d+")) {
                                            uniqueValues.add(value);
                                        }
                                    }
                                }
                            }
                            int maxOptionNumber = uniqueValues.stream().mapToInt(Integer::parseInt).max().orElse(5);
                            maxOptionNumber = Math.max(2, Math.min(maxOptionNumber, 10));
                            List<String> dynamicOptions = new ArrayList<>();
                            for (int i = 1; i <= maxOptionNumber; i++) {
                                dynamicOptions.add(String.valueOf(i));
                            }
                            optionHeaders = dynamicOptions;
                        }
                        int optionCount = optionHeaders.size();
                        headers.addAll(optionHeaders);
                        headers.add("平均分");
                        table.setHeaders(headers);

                        List<List<Object>> tableRows = new ArrayList<>();
                        // 统计每个小题
                        List<long[]> allCounts = new ArrayList<>();
                        List<Double> allMeans = new ArrayList<>();
                        if (targetStruct.getSubQuestions() != null) {
                            for (int i = 0; i < targetStruct.getSubQuestions().size(); i++) {
                                SurveyData.SubQuestion subQ = targetStruct.getSubQuestions().get(i);
                                if (subQ != null && i < columnIndices.size()) {
                                    int col = columnIndices.get(i) - 1;
                                    Map<String, Long> freq = dataRows.stream().collect(Collectors.groupingBy(
                                            row -> (col < row.size() && row.get(col) != null && !row.get(col).isEmpty())
                                                    ? row.get(col)
                                                    : null,
                                            Collectors.counting()));
                                    long total = dataRows.stream().filter(
                                            row -> col < row.size() && row.get(col) != null && !row.get(col).isEmpty())
                                            .count();
                                    long[] counts = new long[optionCount];
                                    double sumScore = 0;
                                    for (int j = 0; j < optionCount; j++) {
                                        String key = String.valueOf(j + 1);
                                        counts[j] = freq.getOrDefault(key, 0L);
                                        sumScore += counts[j] * (j + 1);
                                    }
                                    double mean = total > 0 ? sumScore / total : 0.0;
                                    allCounts.add(counts);
                                    allMeans.add(mean);
                                    List<Object> row = new ArrayList<>();
                                    row.add(subQ.getTitle());
                                    for (int j = 0; j < optionCount; j++) {
                                        double percent = total == 0 ? 0 : counts[j] * 100.0 / total;
                                        row.add(String.format("%d(%.2f%%)", counts[j], percent));
                                    }
                                    row.add(String.format("%.2f", mean));
                                    tableRows.add(row);
                                }
                            }
                        }
                        // 小计行
                        long[] totalCounts = new long[optionCount];
                        long totalTotal = 0;
                        for (long[] counts : allCounts) {
                            for (int j = 0; j < optionCount; j++) {
                                totalCounts[j] += counts[j];
                            }
                        }
                        for (long c : totalCounts)
                            totalTotal += c;
                        List<Object> totalRow = new ArrayList<>();
                        totalRow.add("小计");
                        for (int j = 0; j < optionCount; j++) {
                            double percent = totalTotal == 0 ? 0 : totalCounts[j] * 100.0 / totalTotal;
                            totalRow.add(String.format("%d(%.2f%%)", totalCounts[j], percent));
                        }
                        double meanOfMeans = allMeans.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
                        totalRow.add(String.format("%.2f", meanOfMeans));
                        tableRows.add(totalRow);
                        table.setRows(tableRows);
                    } else if ("6multiple".equals(type)) {
                        // 矩阵多选题
                        table.setType("matrix_multiple_statistics");
                        table.setTitle(targetStruct.getNumId() + "." + targetStruct.getTitle() + "[矩阵多选题]");

                        List<String> headers = new ArrayList<>();
                        headers.add("题目\\选项");

                        // 获取选项作为表头 - 优先使用第一个子问题的选项
                        List<String> optionHeaders = null;
                        if (targetStruct.getSubQuestions() != null && !targetStruct.getSubQuestions().isEmpty()) {
                            SurveyData.SubQuestion firstSubQ = targetStruct.getSubQuestions().get(0);
                            if (firstSubQ != null && firstSubQ.getOptions() != null
                                    && !firstSubQ.getOptions().isEmpty()) {
                                optionHeaders = firstSubQ.getOptions();
                            }
                        }

                        // 如果子问题没有选项，则尝试使用主问题的选项
                        if (optionHeaders == null && targetStruct.getOptions() != null
                                && !targetStruct.getOptions().isEmpty()) {
                            optionHeaders = targetStruct.getOptions();
                        }

                        // 如果还是没有选项，使用默认选项
                        if (optionHeaders == null) {
                            // 根据列索引数量和子问题数量动态计算选项数量
                            int subQuestionCount = targetStruct.getSubQuestions() != null
                                    ? targetStruct.getSubQuestions().size()
                                    : 1;
                            int totalColumns = columnIndices.size();
                            int estimatedOptionCount = subQuestionCount > 0 ? totalColumns / subQuestionCount
                                    : totalColumns;

                            // 确保至少有2个选项，最多不超过10个
                            estimatedOptionCount = Math.max(2, Math.min(estimatedOptionCount, 10));

                            List<String> dynamicOptions = new ArrayList<>();
                            for (int i = 1; i <= estimatedOptionCount; i++) {
                                dynamicOptions.add("选项" + i);
                            }
                            optionHeaders = dynamicOptions;
                        }

                        headers.addAll(optionHeaders);
                        headers.add("小计");
                        table.setHeaders(headers);

                        List<List<Object>> tableRows = new ArrayList<>();
                        if (targetStruct.getSubQuestions() != null) {
                            for (int i = 0; i < targetStruct.getSubQuestions().size(); i++) {
                                SurveyData.SubQuestion subQ = targetStruct.getSubQuestions().get(i);
                                if (subQ != null) {
                                    List<Object> row = new ArrayList<>();
                                    row.add(subQ.getTitle());

                                    long total = dataRows.size();

                                    // 使用当前子问题的选项进行统计
                                    List<String> currentOptions = subQ.getOptions();
                                    if (currentOptions == null || currentOptions.isEmpty()) {
                                        currentOptions = optionHeaders;
                                    }

                                    // 确保选项数量一致
                                    int optionCount = Math.min(currentOptions.size(), optionHeaders.size());
                                    long totalSelected = 0;

                                    for (int j = 0; j < optionCount; j++) {
                                        int colIndex = i * optionCount + j;
                                        if (colIndex < columnIndices.size()) {
                                            int col = columnIndices.get(colIndex) - 1;
                                            long count = dataRows.stream().filter(
                                                    rowData -> col < rowData.size() && "1".equals(rowData.get(col)))
                                                    .count();
                                            double percent = total == 0 ? 0 : count * 100.0 / total;
                                            row.add(String.format("%d (%.2f%%)", count, percent));
                                            totalSelected += count;
                                        } else {
                                            row.add("0 (0.0%)");
                                        }
                                    }

                                    // 补充缺失的选项列
                                    for (int j = optionCount; j < optionHeaders.size(); j++) {
                                        row.add("0 (0.0%)");
                                    }

                                    // 计算并添加小计
                                    final int finalI = i;
                                    final int finalOptionCount = optionCount;
                                    long validSubQRespondents = dataRows.stream().filter(rowData -> {
                                        for (int j = 0; j < finalOptionCount; j++) {
                                            int colIndex = finalI * finalOptionCount + j;
                                            if (colIndex < columnIndices.size()) {
                                                int col = columnIndices.get(colIndex) - 1;
                                                if (col < rowData.size() && "1".equals(rowData.get(col))) {
                                                    return true;
                                                }
                                            }
                                        }
                                        return false;
                                    }).count();
                                    row.add(validSubQRespondents);

                                    tableRows.add(row);
                                }
                            }
                        }
                        table.setRows(tableRows);
                    } else {
                        throw new RuntimeException("不支持的题型: " + type);
                    }
                    allResults.add(table);
                } catch (Exception e) {
                    log.error("[frequencyStatistics] 处理题号 {} 时发生错误: {}", questionNum, e.getMessage());
                    FactorAnalysisOutput.TableData errorTable = new FactorAnalysisOutput.TableData();
                    errorTable.setTitle("题号 " + questionNum + " 分析失败");
                    errorTable.setHeaders(Collections.singletonList("错误信息"));
                    errorTable.setRows(Collections.singletonList(Collections.singletonList(e.getMessage())));
                    allResults.add(errorTable);
                }
            }

            saveTableDataToMessage(sessionId, allResults);
            return allResults;
        } catch (Exception e) {
            log.error("[frequencyStatistics] 处理数据时发生严重错误: {}", e.getMessage(), e);
            throw new RuntimeException("处理数据时发生严重错误: " + e.getMessage());
        }
    }

    /**
     * 从数据库获取指定题号的问卷结构信息
     * 
     * @param sessionId   会话ID
     * @param questionNum 题号
     * @return 题目结构信息
     */
    private SurveyStructure getSurveyStructureFromDatabase(String sessionId, Integer questionNum) {
        try {
            // 通过sessionId获取问卷数据
            WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
            if (surveyData == null || surveyData.getJsonData() == null) {
                log.error("[getSurveyStructureFromDatabase] 未找到会话 {} 的问卷数据", sessionId);
                return null;
            }

            List<SurveyData> surveyDataList = surveyData.getJsonData();

            // 根据题号查找对应的题目
            for (SurveyData data : surveyDataList) {
                if (data.getNumId() == questionNum) {
                    // 转换为SurveyStructure对象
                    SurveyStructure structure = new SurveyStructure();
                    structure.setNumId(data.getNumId());
                    structure.setTitle(data.getTitle());
                    structure.setType(data.getType());
                    structure.setTypeInfo(data.getTypeInfo());
                    structure.setOptions(data.getOptions());
                    structure.setColIndices(data.getColIndices());
                    structure.setSubQuestions(data.getSubQuestions());
                    return structure;
                }
            }

            log.error("[getSurveyStructureFromDatabase] 未找到题号为 {} 的题目", questionNum);
            return null;

        } catch (Exception e) {
            log.error("[getSurveyStructureFromDatabase] 获取问卷结构信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取问卷结构信息失败: " + e.getMessage());
        }
    }

    /**
     * Helper to convert Excel-style column name to 1-based index.
     * 
     * @param colName e.g., "A", "B", "AA"
     * @return 1-based column index
     */
    private int excelColToNumber(String colName) {
        int number = 0;
        if (colName == null || colName.isEmpty()) {
            throw new IllegalArgumentException("Column name cannot be empty.");
        }
        for (int i = 0; i < colName.length(); i++) {
            char c = colName.charAt(i);
            if (!Character.isLetter(c)) {
                throw new IllegalArgumentException("Invalid column name format: " + colName);
            }
            number = number * 26 + (c - 'A' + 1);
        }
        return number;
    }

    @Tool(description = "解析用户选定的表格数据范围，返回该范围内的题目结构信息和原始数据。当用户输入包含 '@@这是选中的数据范围...@@' 时使用此工具。支持单个单元格如 'A2' 或区域如 'A1:C10'。")
    public String getRangeContext(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "用户选定的数据范围，格式如 'A1:C10' 或 'A2'") String range) {
        log.info("[getRangeContext] 收到范围解析请求: {}", range);

        if (range == null || range.trim().isEmpty()) {
            throw new IllegalArgumentException("数据范围不能为空");
        }
        range = range.trim().toUpperCase();
        // 支持单格和区域
        if (range.matches("^[A-Z]+[1-9][0-9]*$")) {
            range = range + ":" + range;
        }
        if (!range.matches("^[A-Z]+[1-9][0-9]*:[A-Z]+[1-9][0-9]*$")) {
            throw new IllegalArgumentException("无效的数据范围格式。应为 'A1:C10' 或 'A2' 格式。");
        }

        try {
            // 1. Parse range to get 1-based indices
            String[] parts = range.split(":");
            String startCell = parts[0];
            String endCell = parts[1];

            String startColStr = startCell.replaceAll("\\d", "");
            String endColStr = endCell.replaceAll("\\d", "");
            int startRow = Integer.parseInt(startCell.replaceAll("[A-Z]", ""));
            int endRow = Integer.parseInt(endCell.replaceAll("[A-Z]", ""));

            int startCol = excelColToNumber(startColStr);
            int endCol = excelColToNumber(endColStr);

            // 2. Get full Excel data
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message == null || message.getCompleteExcelData() == null) {
                throw new RuntimeException("未找到对应的Excel数据");
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                    new TypeReference<>() {
                    });

            // 3. Extract selected data slice
            int dataStartRowIndex = startRow - 1;
            int dataEndRowIndex = Math.min(endRow, excelData.size());

            List<List<String>> selectedData = new ArrayList<>();
            if (dataStartRowIndex < dataEndRowIndex) {
                excelData.subList(dataStartRowIndex, dataEndRowIndex).forEach(fullRow -> {
                    int colStartIndex = startCol - 1;
                    int colEndIndex = Math.min(endCol, fullRow.size());
                    if (colStartIndex < colEndIndex) {
                        selectedData.add(new ArrayList<>(fullRow.subList(colStartIndex, colEndIndex)));
                    } else {
                        selectedData.add(new ArrayList<>());
                    }
                });
            }

            // 新增：获取所选范围的表头
            List<String> selectedHeaders = new ArrayList<>();
            if (!excelData.isEmpty()) {
                List<String> headerRow = excelData.get(0);
                int colStartIndex = startCol - 1;
                int colEndIndex = Math.min(endCol, headerRow.size());
                if (colStartIndex < colEndIndex) {
                    selectedHeaders = new ArrayList<>(headerRow.subList(colStartIndex, colEndIndex));
                }
            }

            // 4. Get and filter survey structure
            WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
            List<SurveyData> allStructures = (surveyData != null && surveyData.getJsonData() != null)
                    ? surveyData.getJsonData()
                    : Collections.emptyList();

            List<SurveyData> relevantStructures = allStructures.stream()
                    .filter(sd -> sd.getColIndices() != null && !sd.getColIndices().isEmpty())
                    .filter(sd -> sd.getColIndices().stream().anyMatch(idx -> idx >= startCol && idx <= endCol))
                    .collect(Collectors.toList());

            // 5. Build and serialize response
            RangeContext response = new RangeContext();
            response.setStructures(relevantStructures);
            response.setData(selectedData);
            response.setStartRow(startRow);
            response.setStartCol(startCol);
            response.setEndRow(endRow);
            response.setEndCol(endCol);
            // 新增：设置表头
            response.setSelectedHeaders(selectedHeaders);

            log.info("[getRangeContext] 返回范围解析结果: {}",
                    objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(response));

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(response);

        } catch (Exception e) {
            log.error("[getRangeContext] 范围解析时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("解析数据范围时发生错误: " + e.getMessage());
        }
    }

    @Tool(description = "获取完整的问卷结构信息，包括所有题目的题号、标题、类型、选项和对应的列号，帮助AI理解问卷结构并准确定位列号")
    public String getSurveyStructureInfo(
            @ToolParam(description = "会话ID") String sessionId) {
        log.info("[getSurveyStructureInfo] 获取问卷结构信息，sessionId={}", sessionId);

        try {
            WjxSurveyData surveyData = aiChatService.getSurveyDataBySessionId(sessionId);
            if (surveyData == null || surveyData.getJsonData() == null) {
                throw new RuntimeException("未找到会话 " + sessionId + " 的问卷数据");
            }

            List<SurveyData> structureList = surveyData.getJsonData();

            // 构建结构化的问卷信息
            Map<String, Object> surveyInfo = new HashMap<>();
            surveyInfo.put("sessionId", sessionId);
            surveyInfo.put("totalQuestions", structureList.size());

            List<Map<String, Object>> questions = new ArrayList<>();
            for (SurveyData data : structureList) {
                Map<String, Object> question = new HashMap<>();
                question.put("numId", data.getNumId());
                question.put("title", data.getTitle());
                question.put("type", data.getType());
                question.put("typeInfo", data.getTypeInfo());
                question.put("options", data.getOptions());
                question.put("colIndices", data.getColIndices());
                question.put("subQuestions", data.getSubQuestions());
                questions.add(question);
            }
            surveyInfo.put("questions", questions);

            // 获取Excel表头信息
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(
                            message.getCompleteExcelData(),
                            new TypeReference<List<List<String>>>() {
                            });

                    if (!excelData.isEmpty()) {
                        List<String> headers = excelData.get(0);
                        Map<String, Object> excelInfo = new HashMap<>();
                        excelInfo.put("totalColumns", headers.size());
                        excelInfo.put("headers", headers);

                        // 构建列号到表头的映射
                        Map<Integer, String> colToHeader = new HashMap<>();
                        for (int i = 0; i < headers.size(); i++) {
                            colToHeader.put(i + 1, headers.get(i)); // 1-based列号
                        }
                        excelInfo.put("colToHeader", colToHeader);

                        surveyInfo.put("excelInfo", excelInfo);
                    }
                }
            } catch (Exception e) {
                log.warn("[getSurveyStructureInfo] 获取Excel表头信息失败: {}", e.getMessage());
            }

            ObjectMapper mapper = new ObjectMapper();
            String result = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(surveyInfo);

            log.info("[getSurveyStructureInfo] 成功获取问卷结构信息，包含 {} 个题目", structureList.size());
            return result;

        } catch (Exception e) {
            log.error("[getSurveyStructureInfo] 获取问卷结构信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取问卷结构信息失败: " + e.getMessage());
        }
    }

    @Tool(description = "交叉（卡方）分析，支持单题和多题批量交叉，自动生成分组频数表、百分比表和卡方检验结果")
    public List<FactorAnalysisOutput.TableData> crossTabChiSquareAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "题目列索引列表（从1开始）") List<Integer> questionCols,
            @ToolParam(description = "分组变量列索引（从1开始，可多个）") List<Integer> groupCols) {
        log.info("[crossTabChiSquareAnalysis] sessionId={}, questionCols={}, groupCols={}", sessionId, questionCols,
                groupCols);
        if (questionCols.isEmpty() || groupCols.isEmpty()) {
            throw new RuntimeException("主变量和分组变量都不能为空");
        }
        List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
        try {
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message == null || message.getCompleteExcelData() == null) {
                log.error("[crossTabChiSquareAnalysis] 未找到完整Excel数据，sessionId={}", sessionId);
                throw new RuntimeException("未找到对应的Excel数据");
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                    new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                    });
            log.info("[crossTabChiSquareAnalysis] excelData行数={}，前两行={} {}", excelData.size(),
                    excelData.size() > 0 ? excelData.get(0) : null,
                    excelData.size() > 1 ? excelData.get(1) : null);
            if (excelData.size() < 2)
                throw new RuntimeException("数据不足");
            List<String> header = excelData.get(0);
            List<List<String>> dataRows = excelData.subList(1, excelData.size());

            for (Integer mainCol : questionCols) {
                for (Integer groupCol : groupCols) {
                    int mainColIdx = mainCol - 1;
                    int groupColIdx = groupCol - 1;
                    SurveyStructure mainStruct = getSurveyStructureFromDatabase(sessionId, mainCol);
                    SurveyStructure groupStruct = getSurveyStructureFromDatabase(sessionId, groupCol);
                    log.info("[crossTabChiSquareAnalysis] mainColIdx={}, groupColIdx={}, mainStruct={}, groupStruct={}",
                            mainColIdx, groupColIdx, mainStruct, groupStruct);
                    List<String> mainOptions = mainStruct != null && mainStruct.getOptions() != null
                            ? mainStruct.getOptions()
                            : Collections.emptyList();
                    List<String> groupOptions = groupStruct != null && groupStruct.getOptions() != null
                            ? groupStruct.getOptions()
                            : Collections.emptyList();
                    // 编码值到选项文本的映射
                    Map<String, String> mainValueMap = new HashMap<>();
                    if (mainStruct != null && mainStruct.getOptions() != null) {
                        for (int i = 0; i < mainStruct.getOptions().size(); i++) {
                            mainValueMap.put(String.valueOf(i + 1), mainStruct.getOptions().get(i));
                        }
                    }
                    Map<String, String> groupValueMap = new HashMap<>();
                    if (groupStruct != null && groupStruct.getOptions() != null) {
                        for (int i = 0; i < groupStruct.getOptions().size(); i++) {
                            groupValueMap.put(String.valueOf(i + 1), groupStruct.getOptions().get(i));
                        }
                    }
                    String mainTitle = mainStruct != null && mainStruct.getTitle() != null ? mainStruct.getTitle()
                            : header.get(mainColIdx);
                    String groupTitle = groupStruct != null && groupStruct.getTitle() != null ? groupStruct.getTitle()
                            : header.get(groupColIdx);
                    log.info("[crossTabChiSquareAnalysis] mainOptions={}, groupOptions={}", mainOptions, groupOptions);

                    // 统计频数（忽略大小写和空格）
                    Map<String, Map<String, Integer>> freq = new LinkedHashMap<>();
                    Map<String, Integer> groupTotals = new LinkedHashMap<>();
                    for (String mOpt : mainOptions) {
                        freq.put(mOpt, new LinkedHashMap<>());
                        for (String gOpt : groupOptions) {
                            freq.get(mOpt).put(gOpt, 0);
                        }
                    }
                    for (String gOpt : groupOptions) {
                        groupTotals.put(gOpt, 0);
                    }
                    for (List<String> row : dataRows) {
                        String mVal = (mainColIdx < row.size()) ? row.get(mainColIdx) : null;
                        String gVal = (groupColIdx < row.size()) ? row.get(groupColIdx) : null;
                        // 新增：编码转文本
                        if (mainValueMap.containsKey(mVal))
                            mVal = mainValueMap.get(mVal);
                        if (groupValueMap.containsKey(gVal))
                            gVal = groupValueMap.get(gVal);
                        String mValTrim = (mVal != null) ? mVal.trim() : null;
                        String gValTrim = (gVal != null) ? gVal.trim() : null;
                        // 选项匹配忽略大小写
                        String mKey = mainOptions.stream().filter(opt -> opt.equalsIgnoreCase(mValTrim)).findFirst()
                                .orElse(null);
                        String gKey = groupOptions.stream().filter(opt -> opt.equalsIgnoreCase(gValTrim)).findFirst()
                                .orElse(null);
                        if (mKey != null && gKey != null) {
                            freq.get(mKey).put(gKey, freq.get(mKey).get(gKey) + 1);
                            groupTotals.put(gKey, groupTotals.get(gKey) + 1);
                        }
                    }
                    log.info("[crossTabChiSquareAnalysis] freq={}, groupTotals={}", freq, groupTotals);

                    // 构建两层表头
                    List<List<Object>> rows = new ArrayList<>();
                    List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
                    List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
                    int totalCols = 1 + groupOptions.size();

                    // 第一行表头
                    List<Object> headRow1 = new ArrayList<>();
                    headRow1.add(mainTitle); // 主变量标题
                    headRow1.add(groupTitle); // 分组变量标题
                    for (int i = 2; i < totalCols; i++)
                        headRow1.add("");
                    headRow1.add(""); // 多一列给合计
                    rows.add(headRow1);
                    List<FactorAnalysisOutput.CellStyle> styleRow1 = new ArrayList<>();
                    styleRow1.add(newCellStyle("bold", null));
                    styleRow1.add(newCellStyle("bold", null));
                    for (int i = 2; i < totalCols; i++)
                        styleRow1.add(newCellStyle("bold", null));
                    styleRow1.add(newCellStyle("bold", null)); // 合计列加粗
                    cellStyles.add(styleRow1);
                    // 合并主变量标题(rowspan=2, colspan=1)
                    cellMerges.add(newCellMerge(0, 0, 2, 1));
                    // 合并分组变量标题(rowspan=1, colspan=groupOptions.size()+1)
                    cellMerges.add(newCellMerge(0, 1, 1, groupOptions.size() + 1));

                    // 第二行表头
                    List<Object> headRow2 = new ArrayList<>();
                    headRow2.add("");
                    for (String gOpt : groupOptions)
                        headRow2.add(gOpt);
                    headRow2.add("合计"); // 右上角合计
                    rows.add(headRow2);
                    List<FactorAnalysisOutput.CellStyle> styleRow2 = new ArrayList<>();
                    styleRow2.add(newCellStyle("bold", null));
                    for (int i = 0; i < groupOptions.size(); i++)
                        styleRow2.add(newCellStyle("bold", null));
                    styleRow2.add(newCellStyle("bold", null)); // 合计列加粗
                    cellStyles.add(styleRow2);

                    // 数据区
                    List<Integer> rowSums = new ArrayList<>();
                    for (int rowIdx = 0; rowIdx < mainOptions.size(); rowIdx++) {
                        String mOpt = mainOptions.get(rowIdx);
                        List<Object> row = new ArrayList<>();
                        List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                        row.add(mOpt); // 只加主变量选项，不再加主变量标题
                        styleRow.add(newCellStyle("bold", null)); // 第一列加粗
                        int rowSum = 0;
                        for (int colIdx = 0; colIdx < groupOptions.size(); colIdx++) {
                            String gOpt = groupOptions.get(colIdx);
                            int cnt = freq.get(mOpt).get(gOpt);
                            int colTotal = groupTotals.get(gOpt); // 列合计
                            double percent = colTotal == 0 ? 0 : cnt * 100.0 / colTotal;
                            row.add(cnt + "(" + String.format("%.2f", percent) + ")");
                            styleRow.add(newCellStyle(null, null));
                            rowSum += cnt;
                        }
                        row.add(rowSum); // 行合计
                        styleRow.add(newCellStyle("bold", null)); // 合计列加粗
                        rowSums.add(rowSum);
                        rows.add(row);
                        cellStyles.add(styleRow);
                    }
                    // 合计行（每列合计，右下角为总合计）
                    List<Object> totalRow = new ArrayList<>();
                    List<FactorAnalysisOutput.CellStyle> totalStyleRow = new ArrayList<>();
                    totalRow.add("合计");
                    totalStyleRow.add(newCellStyle("bold", null));
                    int grandTotal = 0;
                    for (int colIdx = 0; colIdx < groupOptions.size(); colIdx++) {
                        String gOpt = groupOptions.get(colIdx);
                        int colTotal = groupTotals.get(gOpt);
                        totalRow.add(colTotal);
                        totalStyleRow.add(newCellStyle("bold", null));
                        grandTotal += colTotal;
                    }
                    totalRow.add(grandTotal); // 总合计
                    totalStyleRow.add(newCellStyle("bold", null));
                    rows.add(totalRow);
                    cellStyles.add(totalStyleRow);
                    // 统计量行（最后一行整行合并）
                    List<Object> statRow = new ArrayList<>();
                    // 统计量
                    long[][] counts = new long[mainOptions.size()][groupOptions.size()];
                    for (int i = 0; i < mainOptions.size(); i++) {
                        for (int j = 0; j < groupOptions.size(); j++) {
                            counts[i][j] = freq.get(mainOptions.get(i)).get(groupOptions.get(j));
                        }
                    }
                    double chi2 = 0;
                    double p = 1;
                    try {
                        org.apache.commons.math3.stat.inference.ChiSquareTest chiTest = new org.apache.commons.math3.stat.inference.ChiSquareTest();
                        chi2 = chiTest.chiSquare(counts);
                        p = chiTest.chiSquareTest(counts);
                    } catch (Exception e) {
                    }
                    String statStr = String.format("χ²=%.3f p=%.3f %s", chi2, p, (p < 0.05 ? "<0.05" : ">=0.05"));
                    statRow.add(statStr);
                    for (int i = 1; i < groupOptions.size() + 2; i++)
                        statRow.add("");
                    rows.add(statRow);
                    List<FactorAnalysisOutput.CellStyle> statStyleRow = new ArrayList<>();
                    statStyleRow.add(newCellStyle("bold", "#d62728"));
                    for (int i = 1; i < groupOptions.size() + 2; i++)
                        statStyleRow.add(newCellStyle(null, null));
                    cellStyles.add(statStyleRow);
                    // 统计量行横跨所有列
                    cellMerges.add(newCellMerge(rows.size() - 1, 0, 1, groupOptions.size() + 2));

                    // 组装TableData
                    FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
                    table.setType("cross_tab_chisq");
                    table.setTitle(mainTitle + " × " + groupTitle + " 交叉(卡方)分析");
                    // headers只用于前端导出，交叉分析类型不设置headers
                    table.setHeaders(null);
                    table.setRows(rows);
                    table.setCellMerges(cellMerges);
                    table.setCellStyles(cellStyles);
                    log.info("[crossTabChiSquareAnalysis] 最终rows={}", rows);
                    resultTables.add(table);
                }
            }
            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[交叉分析] 处理数据时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("交叉分析异常: " + e.getMessage());
        }
    }

    // 辅助方法：生成CellMerge对象
    private FactorAnalysisOutput.CellMerge newCellMerge(int row, int col, int rowspan, int colspan) {
        FactorAnalysisOutput.CellMerge merge = new FactorAnalysisOutput.CellMerge();
        merge.setRow(row);
        merge.setCol(col);
        merge.setRowspan(rowspan);
        merge.setColspan(colspan);
        return merge;
    }

    // 新增：单元格样式辅助方法
    private FactorAnalysisOutput.CellStyle newCellStyle(String fontWeight, String color) {
        FactorAnalysisOutput.CellStyle style = new FactorAnalysisOutput.CellStyle();
        style.setFontWeight(fontWeight);
        style.setColor(color);
        return style;
    }

    /**
     * 批量相关性分析，生成相关性矩阵，表头和行名均为题目标题，左侧为均值、标准差，右侧为相关系数矩阵，显著性星号。
     * method可用逗号分隔指定多个相关系数类型，如'pearson,spearman'，未指定时全部分析
     * 
     * @param sessionId  会话ID
     * @param colIndices 题目列索引列表（从1开始）
     * @param method     相关系数类型，可选：pearson、spearman、kendall，可用逗号分隔多个，未指定时全部分析
     * @return 相关性分析结果表格
     */
    @Tool(description = "批量相关性分析，生成相关性矩阵，表头和行名均为题目标题，左侧为均值、标准差，右侧为相关系数矩阵，显著性星号。method可用逗号分隔指定多个相关系数类型，如'pearson,spearman'，未指定时全部分析")
    public List<FactorAnalysisOutput.TableData> correlationMatrixAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "题目列索引列表（从1开始）") List<Integer> colIndices,
            @ToolParam(description = "相关系数类型，可选：pearson、spearman、kendall，可用逗号分隔多个，未指定时全部分析") String method) {
        log.info("[correlationMatrixAnalysis]相关性分析sessionId={}, colIndices={}, method={}", sessionId, colIndices,
                method);
        List<String> methodList = new ArrayList<>();
        if (method == null || method.trim().isEmpty()) {
            methodList = Arrays.asList("pearson", "spearman", "kendall");
        } else {
            methodList = Arrays.stream(method.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(String::toLowerCase)
                    .distinct()
                    .collect(Collectors.toList());
        }
        List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
        int methodCount = methodList.size();
        for (int methodIdx = 0; methodIdx < methodList.size(); methodIdx++) {
            String m = methodList.get(methodIdx);
            // 获取表头
            List<String> headers = new ArrayList<>();
            List<String> titles = new ArrayList<>();
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                            new TypeReference<List<List<String>>>() {
                            });
                    if (!excelData.isEmpty()) {
                        List<String> excelHeaders = excelData.get(0);
                        for (Integer idx : colIndices) {
                            if (idx - 1 < excelHeaders.size()) {
                                titles.add(excelHeaders.get(idx - 1));
                            } else {
                                titles.add("题目" + idx);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                for (Integer idx : colIndices)
                    titles.add("题目" + idx);
            }
            headers.add("");
            headers.add("平均值");
            headers.add("标准差");
            headers.addAll(titles);
            List<List<Double>> allData = new ArrayList<>();
            for (Integer idx : colIndices) {
                List<Double> colData = getNumericColumnData(sessionId, idx - 1);
                allData.add(colData);
            }
            int n = colIndices.size();
            List<Double> means = new ArrayList<>();
            List<Double> stds = new ArrayList<>();
            for (List<Double> col : allData) {
                DescriptiveStatistics stats = new DescriptiveStatistics();
                for (Double v : col)
                    if (v != null)
                        stats.addValue(v);
                means.add(stats.getN() > 0 ? stats.getMean() : null);
                stds.add(stats.getN() > 0 ? stats.getStandardDeviation() : null);
            }
            String[][] corrStr = new String[n][n];
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    if (i == j) {
                        corrStr[i][j] = "1";
                    } else if (i > j) {
                        List<Double> x = allData.get(i);
                        List<Double> y = allData.get(j);
                        int minLen = Math.min(x.size(), y.size());
                        double[] arr1 = new double[minLen];
                        double[] arr2 = new double[minLen];
                        for (int k = 0, p = 0; k < minLen; k++) {
                            if (x.get(k) != null && y.get(k) != null) {
                                arr1[p] = x.get(k);
                                arr2[p] = y.get(k);
                                p++;
                            }
                        }
                        double corr = Double.NaN;
                        switch (m) {
                            case "spearman":
                                corr = new SpearmansCorrelation().correlation(arr1, arr2);
                                break;
                            case "kendall":
                                corr = new KendallsCorrelation().correlation(arr1, arr2);
                                break;
                            default:
                                corr = new PearsonsCorrelation().correlation(arr1, arr2);
                        }
                        double pValue = calculateCorrelationPValue(corr, minLen);
                        String sig = pValue < 0.01 ? "**" : (pValue < 0.05 ? "*" : "");
                        corrStr[i][j] = String.format("%.3f%s", corr, sig);
                    } else {
                        corrStr[i][j] = "";
                    }
                }
            }
            List<List<Object>> rows = new ArrayList<>();
            List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            for (int i = 0; i < n; i++) {
                List<Object> row = new ArrayList<>();
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                row.add(titles.get(i));
                styleRow.add(newCellStyle(null, null));
                row.add(means.get(i) == null ? "" : String.format("%.3f", means.get(i)));
                styleRow.add(newCellStyle(null, null));
                row.add(stds.get(i) == null ? "" : String.format("%.3f", stds.get(i)));
                styleRow.add(newCellStyle(null, null));
                for (int j = 0; j < n; j++) {
                    row.add(corrStr[i][j]);
                    styleRow.add(newCellStyle(null, null));
                }
                rows.add(row);
                cellStyles.add(styleRow);
            }
            // 添加规则说明行
            String rule = "*表示在0.05水平上统计显著，**表示在0.01水平上统计显著。";
            List<Object> ruleRow = new ArrayList<>();
            ruleRow.add(rule);
            int totalCols = n + 3; // 题目+均值+标准差+n个题目
            for (int i = 1; i < totalCols; i++)
                ruleRow.add("");
            rows.add(ruleRow);
            // 合并最后一行所有单元格
            cellMerges.add(newCellMerge(rows.size() - 1, 0, 1, totalCols));
            // 最后一行样式：红色、加粗、居中
            List<FactorAnalysisOutput.CellStyle> ruleStyleRow = new ArrayList<>();
            for (int i = 0; i < totalCols; i++) {
                FactorAnalysisOutput.CellStyle style = newCellStyle("bold", "#d62728");
                ruleStyleRow.add(style);
            }
            cellStyles.add(ruleStyleRow);
            FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
            table.setType("correlation_matrix");
            String methodName = "Pearson".equalsIgnoreCase(m) ? "Pearson相关性矩阵"
                    : ("Spearman".equalsIgnoreCase(m) ? "Spearman相关性矩阵" : "Kendall相关性矩阵");
            table.setTitle(methodName);
            table.setHeaders(headers);
            table.setRows(rows);
            table.setCellMerges(cellMerges);
            table.setCellStyles(cellStyles);
            resultTables.add(table);
        }
        saveTableDataToMessage(sessionId, resultTables);
        return resultTables;
    }

    @Tool(description = "线性回归分析，支持多元回归，生成标准回归分析表格")
    public List<FactorAnalysisOutput.TableData> performRegression(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始）") List<Integer> independentVars) {
        log.info("[performRegression]线性回归分析 sessionId={}, dependentVar={}, independentVars={}", sessionId, dependentVar,
                independentVars);
        try {
            // 1. 获取数据
            List<Double> y = getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xList = new ArrayList<>();
            for (Integer col : independentVars) {
                xList.add(getNumericColumnData(sessionId, col - 1));
            }
            int n = y.size();
            int k = xList.size();
            // 2. 组装X矩阵
            double[][] X = new double[n][k];
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < k; j++) {
                    X[i][j] = (i < xList.get(j).size() && xList.get(j).get(i) != null) ? xList.get(j).get(i) : 0.0;
                }
            }
            double[] Y = new double[n];
            for (int i = 0; i < n; i++) {
                Y[i] = (i < y.size() && y.get(i) != null) ? y.get(i) : 0.0;
            }
            // 3. 回归分析
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regression = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            regression.newSampleData(Y, X);
            double[] beta = regression.estimateRegressionParameters(); // 包含截距
            double[] stdErr = regression.estimateRegressionParametersStandardErrors();
            double[] tVals = new double[beta.length];
            double[] pVals = new double[beta.length];
            double[] stdBeta = new double[beta.length];
            // 计算标准化系数
            double yStd = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics(Y).getStandardDeviation();
            for (int j = 1; j < beta.length; j++) {
                org.apache.commons.math3.stat.descriptive.DescriptiveStatistics xStats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                for (int i = 0; i < n; i++) {
                    xStats.addValue(X[i][j - 1]);
                }
                double xStd = xStats.getStandardDeviation();
                stdBeta[j] = beta[j] * xStd / yStd;
            }
            stdBeta[0] = Double.NaN; // 截距无标准化系数
            // t值和p值
            org.apache.commons.math3.distribution.TDistribution tDist = new org.apache.commons.math3.distribution.TDistribution(
                    n - k - 1);
            for (int j = 0; j < beta.length; j++) {
                tVals[j] = beta[j] / stdErr[j];
                pVals[j] = 2 * (1 - tDist.cumulativeProbability(Math.abs(tVals[j])));
            }
            // 计算VIF和容忍度
            double[] vifs = new double[k];
            double[] tolerances = new double[k];
            for (int j = 0; j < k; j++) {
                // 以第j个自变量为因变量，其余为自变量做回归，R2
                List<List<Double>> xOther = new ArrayList<>();
                for (int m = 0; m < k; m++)
                    if (m != j)
                        xOther.add(xList.get(m));
                List<Double> xj = xList.get(j);
                double[][] Xj = new double[n][xOther.size()];
                for (int i = 0; i < n; i++) {
                    for (int m = 0; m < xOther.size(); m++) {
                        Xj[i][m] = (i < xOther.get(m).size() && xOther.get(m).get(i) != null) ? xOther.get(m).get(i)
                                : 0.0;
                    }
                }
                double[] Yj = new double[n];
                for (int i = 0; i < n; i++) {
                    Yj[i] = (i < xj.size() && xj.get(i) != null) ? xj.get(i) : 0.0;
                }
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regJ = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regJ.newSampleData(Yj, Xj);
                double r2 = regJ.calculateRSquared();
                tolerances[j] = 1 - r2;
                vifs[j] = 1 / tolerances[j];
            }
            // 4. 获取变量名
            List<String> varNames = new ArrayList<>();
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                            });
                    List<String> headers = excelData.get(0);
                    varNames.add("常数");
                    for (int i = 0; i < independentVars.size(); i++) {
                        Integer idx = independentVars.get(i);
                        String title = (idx - 1 < headers.size() && headers.get(idx - 1) != null
                                && !headers.get(idx - 1).trim().isEmpty()) ? headers.get(idx - 1) : ("变量" + idx);
                        varNames.add((idx) + ". " + title);
                    }
                }
            } catch (Exception e) {
                varNames.add("常数");
                for (Integer idx : independentVars)
                    varNames.add(idx + ". 变量" + idx);
            }
            // 5. 构建表格（表头两行全部用rows和cellMerges实现）
            List<List<Object>> rows = new ArrayList<>();
            // 第一行表头
            List<Object> headRow1 = new ArrayList<>();
            headRow1.add("");
            headRow1.add("非标准化系数");
            headRow1.add("");
            headRow1.add("标准化系数");
            headRow1.add("t");
            headRow1.add("p");
            headRow1.add("共线性诊断");
            headRow1.add("");
            rows.add(headRow1);
            // 第二行表头
            List<Object> headRow2 = new ArrayList<>();
            headRow2.add("");
            headRow2.add("B");
            headRow2.add("标准误");
            headRow2.add("Beta");
            headRow2.add("");
            headRow2.add("");
            headRow2.add("VIF");
            headRow2.add("容忍度");
            rows.add(headRow2);
            // 数据行（下标+2）
            for (int i = 0; i < beta.length; i++) {
                List<Object> row = new ArrayList<>();
                row.add(varNames.get(i));
                row.add(String.format("%.3f", beta[i]));
                row.add(String.format("%.3f", stdErr[i]));
                row.add(i == 0 ? "-" : String.format("%.3f", stdBeta[i]));
                row.add(String.format("%.3f", tVals[i]));
                String sig = pVals[i] < 0.01 ? "**" : (pVals[i] < 0.05 ? "*" : "");
                row.add((pVals[i] < 0.001 ? "0.000" : String.format("%.3f", pVals[i])) + sig);
                row.add(i == 0 ? "-" : String.format("%.3f", vifs[i - 1]));
                row.add(i == 0 ? "-" : String.format("%.3f", tolerances[i - 1]));
                rows.add(row);
            }
            // R2、调整R2、F、D-W、备注、规则说明
            double r2 = regression.calculateRSquared();
            double adjR2 = regression.calculateAdjustedRSquared();
            double ess = regression.calculateTotalSumOfSquares() - regression.calculateResidualSumOfSquares();
            double rss = regression.calculateResidualSumOfSquares();
            double f = ((ess / k) / (rss / (n - k - 1)));
            double dw = 0.0;
            try {
                double sumDiff2 = 0.0, sumE2 = 0.0;
                double[] residuals = regression.estimateResiduals();
                for (int i = 1; i < residuals.length; i++) {
                    sumDiff2 += Math.pow(residuals[i] - residuals[i - 1], 2);
                }
                for (int i = 0; i < residuals.length; i++) {
                    sumE2 += Math.pow(residuals[i], 2);
                }
                dw = sumE2 == 0 ? 0.0 : sumDiff2 / sumE2;
            } catch (Exception e) {
            }
            List<Object> r2Row = new ArrayList<>();
            r2Row.add("R²");
            r2Row.add(String.format("%.3f", r2));
            for (int i = 2; i < 8; i++)
                r2Row.add("");
            rows.add(r2Row);
            List<Object> adjR2Row = new ArrayList<>();
            adjR2Row.add("调整R²");
            adjR2Row.add(String.format("%.3f", adjR2));
            for (int i = 2; i < 8; i++)
                adjR2Row.add("");
            rows.add(adjR2Row);
            String fStr = String.format("F (%d,%d)=%.3f,p=%s", k, n - k - 1, f, (f > 0.0 ? "0.000" : "-"));
            List<Object> fRow = new ArrayList<>();
            fRow.add("F");
            fRow.add(fStr);
            for (int i = 2; i < 8; i++)
                fRow.add("");
            rows.add(fRow);
            List<Object> dwRow = new ArrayList<>();
            dwRow.add("D-W值");
            dwRow.add(String.format("%.3f", dw));
            for (int i = 2; i < 8; i++)
                dwRow.add("");
            rows.add(dwRow);
            String depVarTitle = "";
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                            });
                    List<String> depHeaders = excelData.get(0);
                    if (dependentVar - 1 < depHeaders.size()) {
                        depVarTitle = depHeaders.get(dependentVar - 1);
                    }
                }
            } catch (Exception e) {
            }
            String depVarNote = "备注：因变量 = " + dependentVar + "、" + (depVarTitle == null ? "" : depVarTitle);
            String rule = "*表示在0.05水平上统计显著，**表示在0.01水平上统计显著。";
            List<Object> ruleRow = new ArrayList<>();
            ruleRow.add(depVarNote + "，" + rule);
            for (int i = 1; i < 8; i++)
                ruleRow.add("");
            rows.add(ruleRow);
            // 合并单元格
            List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            // 一级表头合并
            cellMerges.add(newCellMerge(0, 0, 2, 1));
            cellMerges.add(newCellMerge(0, 1, 1, 2)); // 非标准化系数
            cellMerges.add(newCellMerge(0, 4, 2, 1)); // t
            cellMerges.add(newCellMerge(0, 5, 2, 1)); // p
            cellMerges.add(newCellMerge(0, 6, 1, 2)); // 共线性诊断
            // 二级表头VIF、容忍度不合并
            // 规则说明行合并
            cellMerges.add(newCellMerge(rows.size() - 1, 0, 1, 8));
            // 倒数2~5行合并
            cellMerges.add(newCellMerge(rows.size() - 5, 1, 1, 7));
            cellMerges.add(newCellMerge(rows.size() - 4, 1, 1, 7));
            cellMerges.add(newCellMerge(rows.size() - 3, 1, 1, 7));
            cellMerges.add(newCellMerge(rows.size() - 2, 1, 1, 7));
            // 单元格样式
            List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int j = 0; j < 8; j++) {
                    FactorAnalysisOutput.CellStyle style = newCellStyle(null, null);
                    if (i == rows.size() - 1)
                        style = newCellStyle("bold", "#d62728");
                    if (i == rows.size() - 2)
                        style = newCellStyle("bold", null);
                    if (i > 1 && j == 5 && rows.get(i).get(5) != null && rows.get(i).get(5).toString().contains("*"))
                        style = newCellStyle("bold", "#d62728");
                    styleRow.add(style);
                }
                cellStyles.add(styleRow);
            }
            // 组装TableData
            FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
            table.setType("regression");
            table.setTitle("线性回归分析结果 (n=" + n + ")");
            table.setRows(rows);
            table.setCellMerges(cellMerges);
            table.setCellStyles(cellStyles);
            List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
            resultTables.add(table);

            // ========== 新增：线性回归分析结果-简化格式 ==========
            List<Object> subHeader = Arrays.asList("", "回归系数", "95% CI", "共线性诊断", "");
            List<Object> subHeader2 = Arrays.asList("", "", "", "VIF", "容忍度");
            List<List<Object>> simpleRows = new ArrayList<>();
            simpleRows.add(subHeader);
            simpleRows.add(subHeader2);
            for (int i = 0; i < beta.length; i++) {
                double ciLow = beta[i] - 1.96 * stdErr[i];
                double ciHigh = beta[i] + 1.96 * stdErr[i];
                String ciStr = String.format("%.3f ~ %.3f", ciLow, ciHigh);
                String sig = pVals[i] < 0.01 ? "**" : (pVals[i] < 0.05 ? "*" : "");
                String coefStr = String.format("%.3f%s (%.3f)", beta[i], sig, tVals[i]);
                String vifStr = (i == 0 ? "-" : String.format("%.3f", vifs[i - 1]));
                String tolStr = (i == 0 ? "-" : String.format("%.3f", tolerances[i - 1]));
                simpleRows.add(Arrays.asList(
                        varNames.get(i),
                        coefStr,
                        ciStr,
                        vifStr,
                        tolStr));
            }
            // 统计量
            simpleRows.add(Arrays.asList("样本量", n, "", "", ""));
            simpleRows.add(Arrays.asList("R²", String.format("%.3f", r2), "", "", ""));
            simpleRows.add(Arrays.asList("调整R²", String.format("%.3f", adjR2), "", "", ""));
            simpleRows.add(Arrays.asList("F值",
                    String.format("F (%d,%d)=%.3f,p=%.3f", k, n - k - 1, f, (f > 0.0 ? 0.000 : -1.0)), "", "", ""));
            // 备注内容，D-W值写入备注
            String remark = String.format("备注：因变量 = %d，%s D-W值=%.3f", dependentVar, depVarTitle, dw);
            simpleRows.add(Arrays.asList(remark, "", "", "", ""));
            simpleRows.add(Arrays.asList("* p<0.05 ** p<0.01 括号里为t值", "", "", "", ""));
            FactorAnalysisOutput.TableData simpleTable = new FactorAnalysisOutput.TableData();
            simpleTable.setType("regression_simple");
            simpleTable.setTitle("线性回归分析结果-简化格式");
            simpleTable.setRows(simpleRows);
            // ===== 合并单元格和样式 =====
            List<FactorAnalysisOutput.CellMerge> simpleCellMerges = new ArrayList<>();
            simpleCellMerges.add(newCellMerge(0, 0, 2, 1));
            simpleCellMerges.add(newCellMerge(0, 1, 2, 1));
            simpleCellMerges.add(newCellMerge(0, 2, 2, 1));
            simpleCellMerges.add(newCellMerge(0, 3, 1, 2));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 6, 1, 1, 4));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 5, 1, 1, 4));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 4, 1, 1, 4));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 3, 1, 1, 4));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 2, 0, 1, 5));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 1, 0, 1, 5));
            simpleTable.setCellMerges(simpleCellMerges);
            // 样式
            List<List<FactorAnalysisOutput.CellStyle>> simpleCellStyles = new ArrayList<>();
            for (int i = 0; i < simpleRows.size(); i++) {
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int j = 0; j < 5; j++) {
                    if (i == 0 || i == 1)
                        styleRow.add(newCellStyle("bold", null)); // 表头加粗
                    else if (i == simpleRows.size() - 2 || i == simpleRows.size() - 1)
                        styleRow.add(newCellStyle("bold", "#d62728")); // 备注和星号说明红色加粗
                    else
                        styleRow.add(newCellStyle(null, null));
                }
                simpleCellStyles.add(styleRow);
            }
            simpleTable.setCellStyles(simpleCellStyles);
            resultTables.add(simpleTable);

            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[performRegression] 回归分析异常", e);
            throw new RuntimeException("回归分析异常: " + e.getMessage());
        }
    }

    @Tool(description = "支持单题和多题的单因素方差分析（ANOVA），生成合并表头的标准表格")
    public List<FactorAnalysisOutput.TableData> performAnova(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "分组变量列索引（从1开始）") Integer groupCol,
            @ToolParam(description = "被解释变量列索引列表（从1开始，可单题可多题）") List<Integer> valueCols) {
        log.info("[performAnova] 方差分析 sessionId={}, groupCol={}, valueCols={}", sessionId, groupCol, valueCols);
        try {
            // 获取分组数据
            List<String> groupData = getColumnData(sessionId, groupCol - 1);
            // 获取表头
            List<String> headers = new ArrayList<>();
            List<String> excelHeaders = new ArrayList<>();
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                            });
                    if (!excelData.isEmpty()) {
                        excelHeaders = excelData.get(0);
                    }
                }
            } catch (Exception e) {
            }

            // ====== 新增：严格按问卷结构的选项顺序和文本分组 ======
            SurveyStructure groupStruct = getSurveyStructureFromDatabase(sessionId, groupCol);
            List<String> groupOptionLabels = groupStruct != null ? groupStruct.getOptions() : null;
            int optionCount = groupOptionLabels != null ? groupOptionLabels.size() : 0;
            List<String> finalGroupLabels = new ArrayList<>();
            List<Integer> finalGroupSizes = new ArrayList<>();
            List<List<Integer>> finalGroupRowIndices = new ArrayList<>();
            if (optionCount > 0) {
                for (int i = 0; i < optionCount; i++) {
                    String code = String.valueOf(i + 1); // 编码
                    String label = groupOptionLabels.get(i); // 文本
                    List<Integer> rowIdxs = new ArrayList<>();
                    for (int row = 0; row < groupData.size(); row++) {
                        String val = groupData.get(row);
                        if (val != null && val.trim().equals(code)) {
                            rowIdxs.add(row);
                        }
                    }
                    finalGroupLabels.add(label);
                    finalGroupSizes.add(rowIdxs.size());
                    finalGroupRowIndices.add(rowIdxs);
                }
            } else {
                // 回退为原有逻辑
                Map<String, List<Integer>> groupIndexMap = new LinkedHashMap<>();
                for (int i = 0; i < groupData.size(); i++) {
                    String g = groupData.get(i);
                    if (g == null || g.trim().isEmpty())
                        continue;
                    groupIndexMap.computeIfAbsent(g, k -> new ArrayList<>()).add(i);
                }
                List<String> groupNames = new ArrayList<>(groupIndexMap.keySet());
                for (int i = 0; i < groupNames.size(); i++) {
                    String label = groupNames.get(i);
                    List<Integer> idxs = groupIndexMap.get(label);
                    finalGroupLabels.add(label);
                    finalGroupSizes.add(idxs.size());
                    finalGroupRowIndices.add(idxs);
                }
            }
            // ====== END ======

            // 1. 第一行表头（合并单元格）
            List<Object> headRow1 = new ArrayList<>();
            headRow1.add(""); // 左上角空
            String groupTitle = (groupStruct != null && groupStruct.getTitle() != null) ? groupStruct.getTitle() : "";
            Integer groupNumId = (groupStruct != null && groupStruct.getNumId() != null) ? groupStruct.getNumId()
                    : groupCol;
            headRow1.add(String.format("%d. %s(平均值±标准差)", groupNumId, groupTitle));
            for (int i = 0; i < finalGroupLabels.size() - 1; i++) {
                headRow1.add("");
            }
            headRow1.add("F");
            headRow1.add("p");
            // 2. 第二行表头
            List<Object> headRow2 = new ArrayList<>();
            headRow2.add("");
            for (int i = 0; i < finalGroupLabels.size(); i++) {
                headRow2.add(String.format("%s(n=%d)", finalGroupLabels.get(i), finalGroupSizes.get(i)));
            }
            headRow2.add("");
            headRow2.add("");
            // 3. 数据区
            List<List<Object>> dataRows = new ArrayList<>();
            for (int idx = 0; idx < valueCols.size(); idx++) {
                Integer valueCol = valueCols.get(idx);
                List<Double> valueData = getNumericColumnData(sessionId, valueCol - 1);
                String title = (valueCol - 1 < excelHeaders.size() && excelHeaders.get(valueCol - 1) != null
                        && !excelHeaders.get(valueCol - 1).trim().isEmpty()) ? excelHeaders.get(valueCol - 1)
                                : ("题目" + valueCol);
                String rowTitle = title;
                List<Object> row = new ArrayList<>();
                row.add(rowTitle);
                List<List<Double>> groupValues = new ArrayList<>();
                List<Double> groupMeans = new ArrayList<>();
                List<Double> groupStds = new ArrayList<>();
                for (int g = 0; g < finalGroupLabels.size(); g++) {
                    List<Integer> rowIdxs = finalGroupRowIndices.get(g);
                    List<Double> vals = new ArrayList<>();
                    for (Integer i : rowIdxs) {
                        if (i < valueData.size() && valueData.get(i) != null)
                            vals.add(valueData.get(i));
                    }
                    groupValues.add(vals);
                    DescriptiveStatistics stats = new DescriptiveStatistics();
                    for (Double v : vals)
                        stats.addValue(v);
                    groupMeans.add(stats.getMean());
                    groupStds.add(stats.getStandardDeviation());
                    row.add(String.format("%.2f±%.2f", stats.getMean(), stats.getStandardDeviation()));
                }
                // 方差分析F、p
                int k = finalGroupLabels.size();
                int n = valueData.size();
                double grandMean = valueData.stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue)
                        .average().orElse(0.0);
                double ssb = 0.0, ssw = 0.0;
                for (int i = 0; i < k; i++) {
                    double mean = groupMeans.get(i);
                    int size = groupValues.get(i).size();
                    ssb += size * Math.pow(mean - grandMean, 2);
                    for (Double v : groupValues.get(i)) {
                        ssw += Math.pow(v - mean, 2);
                    }
                }
                double msb = ssb / (k - 1);
                double msw = ssw / (n - k);
                double F = msw == 0 ? 0 : msb / msw;
                org.apache.commons.math3.distribution.FDistribution fDist = new org.apache.commons.math3.distribution.FDistribution(
                        k - 1, n - k);
                double p = 1 - fDist.cumulativeProbability(F);
                row.add(String.format("%.3f", F));
                String pStr = String.format("%.3f", p);
                if (p < 0.01)
                    pStr += "**";
                else if (p < 0.05)
                    pStr += "*";
                row.add(pStr);
                while (row.size() < headRow2.size())
                    row.add("");
                if (row.size() > headRow2.size())
                    row = row.subList(0, headRow2.size());
                dataRows.add(row);
            }
            // 4. 显著性说明行
            List<Object> ruleRow = new ArrayList<>();
            ruleRow.add("*p<0.05 **p<0.01");
            for (int i = 1; i < headRow2.size(); i++)
                ruleRow.add("");
            // 5. 合并单元格
            List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            cellMerges.add(newCellMerge(0, 1, 1, finalGroupLabels.size()));
            if (finalGroupLabels.size() > 1)
                cellMerges.add(newCellMerge(0, 0, 2, 1));
            cellMerges.add(newCellMerge(0, finalGroupLabels.size() + 1, 2, 1));
            cellMerges.add(newCellMerge(0, finalGroupLabels.size() + 2, 2, 1));
            cellMerges.add(newCellMerge(dataRows.size() + 2, 0, 1, finalGroupLabels.size() + 3));
            // 6. 单元格样式
            List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            List<FactorAnalysisOutput.CellStyle> styleRow1 = new ArrayList<>();
            for (int i = 0; i < finalGroupLabels.size() + 3; i++)
                styleRow1.add(newCellStyle("bold", null));
            cellStyles.add(styleRow1);
            List<FactorAnalysisOutput.CellStyle> styleRow2 = new ArrayList<>();
            for (int i = 0; i < finalGroupLabels.size() + 3; i++)
                styleRow2.add(newCellStyle("bold", null));
            cellStyles.add(styleRow2);
            for (List<Object> row : dataRows) {
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int i = 0; i < row.size(); i++) {
                    if (i == row.size() - 1 && row.get(i).toString().contains("*"))
                        styleRow.add(newCellStyle("bold", "#d62728"));
                    else
                        styleRow.add(newCellStyle(null, null));
                }
                cellStyles.add(styleRow);
            }
            List<FactorAnalysisOutput.CellStyle> ruleStyleRow = new ArrayList<>();
            for (int i = 0; i < finalGroupLabels.size() + 3; i++)
                ruleStyleRow.add(newCellStyle("bold", null));
            cellStyles.add(ruleStyleRow);
            // 7. 组装TableData
            List<List<Object>> allRows = new ArrayList<>();
            allRows.add(headRow1);
            allRows.add(headRow2);
            allRows.addAll(dataRows);
            allRows.add(ruleRow);
            for (List<Object> r : allRows) {
                while (r.size() < headRow2.size())
                    r.add("");
                if (r.size() > headRow2.size()) {
                    for (int i = r.size() - 1; i >= headRow2.size(); i--)
                        r.remove(i);
                }
            }
            FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
            table.setType("anova");
            table.setTitle("单因素方差分析(ANOVA)");
            table.setRows(allRows);
            table.setCellMerges(cellMerges);
            table.setCellStyles(cellStyles);
            List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
            resultTables.add(table);
            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[performAnova] 方差分析异常", e);
            throw new RuntimeException("方差分析异常: " + e.getMessage());
        }
    }

    @Tool(description = "获取聊天历史记录，帮助AI了解对话上下文和历史。当limit为-1时获取完整历史，否则获取最近N次对话。")
    public List<Map<String, Object>> getChatHistory(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "获取的消息数量限制，默认为10。传入-1表示获取完整历史") Integer limit) {
        log.info("[getChatHistory] 获取聊天记录，sessionId={}, limit={}", sessionId, limit);
        try {
            List<AiChatMessage> messages;
            if (limit != null && limit == -1) {
                // 获取完整历史
                log.info("[getChatHistory] 获取完整聊天历史");
                messages = messageMapper.findBySessionId(sessionId);
            } else {
                // 获取最近N次对话
                int messageLimit = (limit != null && limit > 0) ? limit : 10;
                log.info("[getChatHistory] 获取最近{}次对话", messageLimit);
                // 获取最近的消息，排除最后一条（当前正在处理的消息）
                messages = messageMapper.findRecentMessagesExcludeLast(sessionId, messageLimit);
                // 由于查询是按message_order DESC排序的，需要反转顺序以保持时间顺序
                Collections.reverse(messages);
            }
            // 转换为Map格式，便于AI理解
            List<Map<String, Object>> result = new ArrayList<>();
            for (AiChatMessage msg : messages) {
                Map<String, Object> messageMap = new HashMap<>();
                messageMap.put("role", msg.getRole());
                messageMap.put("content", msg.getContent());
                messageMap.put("createTime", msg.getCreateTime());
                messageMap.put("messageOrder", msg.getMessageOrder());
                // 添加角色中文描述
                String roleText = "用户";
                if ("assistant".equals(msg.getRole())) {
                    roleText = "助手";
                } else if ("system".equals(msg.getRole())) {
                    roleText = "系统";
                }
                messageMap.put("roleText", roleText);
                // 新增：返回changedCells字段
                if (msg.getDataModifications() != null && !msg.getDataModifications().trim().isEmpty()) {
                    messageMap.put("changedCells", msg.getDataModifications());
                }
                result.add(messageMap);
            }
            log.info("[getChatHistory] 成功获取 {} 条消息", result.size());
            return result;
        } catch (Exception e) {
            log.error("[getChatHistory] 获取聊天历史失败", e);
            throw new RuntimeException("获取聊天历史失败: " + e.getMessage());
        }
    }

    /**
     * 独立样本T检验，生成标准表格，支持多题批量分析
     */
    @Tool(description = "独立样本T检验，适用于两组间均值比较，生成标准表格。分组变量必须为两组（如性别），被解释变量可多题批量分析。")
    public List<FactorAnalysisOutput.TableData> performIndependentTTest(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "分组变量列索引（从1开始）") Integer groupCol,
            @ToolParam(description = "被解释变量列索引列表（从1开始，可单题可多题）") List<Integer> valueCols) {
        log.info("[performIndependentTTest] 独立样本T检验 sessionId={}, groupCol={}, valueCols={}", sessionId, groupCol,
                valueCols);
        try {
            // 获取分组数据
            List<String> groupData = getColumnData(sessionId, groupCol - 1);
            // 获取表头
            List<String> excelHeaders = new ArrayList<>();
            try {
                AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
                if (message != null && message.getCompleteExcelData() != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                            new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                            });
                    if (!excelData.isEmpty()) {
                        excelHeaders = excelData.get(0);
                    }
                }
            } catch (Exception e) {
            }

            // 获取分组标签（严格按问卷结构顺序）
            SurveyStructure groupStruct = getSurveyStructureFromDatabase(sessionId, groupCol);
            List<String> groupOptionLabels = groupStruct != null ? groupStruct.getOptions() : null;
            int optionCount = groupOptionLabels != null ? groupOptionLabels.size() : 0;
            List<String> finalGroupLabels = new ArrayList<>();
            List<List<Integer>> finalGroupRowIndices = new ArrayList<>();
            if (optionCount == 2) {
                for (int i = 0; i < 2; i++) {
                    String code = String.valueOf(i + 1);
                    String label = groupOptionLabels.get(i);
                    List<Integer> rowIdxs = new ArrayList<>();
                    for (int row = 0; row < groupData.size(); row++) {
                        String val = groupData.get(row);
                        if (val != null && val.trim().equals(code)) {
                            rowIdxs.add(row);
                        }
                    }
                    finalGroupLabels.add(label + "(n=" + rowIdxs.size() + ")");
                    finalGroupRowIndices.add(rowIdxs);
                }
            } else {
                // 回退为原有逻辑，自动取前两组
                Map<String, List<Integer>> groupIndexMap = new LinkedHashMap<>();
                for (int i = 0; i < groupData.size(); i++) {
                    String g = groupData.get(i);
                    if (g == null || g.trim().isEmpty())
                        continue;
                    groupIndexMap.computeIfAbsent(g, k -> new ArrayList<>()).add(i);
                }
                List<String> groupNames = new ArrayList<>(groupIndexMap.keySet());
                for (int i = 0; i < Math.min(2, groupNames.size()); i++) {
                    String label = groupNames.get(i);
                    List<Integer> idxs = groupIndexMap.get(label);
                    finalGroupLabels.add(label + "(n=" + idxs.size() + ")");
                    finalGroupRowIndices.add(idxs);
                }
            }
            if (finalGroupLabels.size() != 2) {
                throw new RuntimeException("分组变量必须为两组，当前组数：" + finalGroupLabels.size());
            }
            // 1. 第一行表头
            List<Object> headRow1 = new ArrayList<>();
            headRow1.add("");
            String groupTitle = (groupStruct != null && groupStruct.getTitle() != null) ? groupStruct.getTitle() : "分组";
            Integer groupNumId = (groupStruct != null && groupStruct.getNumId() != null) ? groupStruct.getNumId()
                    : groupCol;
            headRow1.add(String.format("%d. %s(平均值±标准差)", groupNumId, groupTitle));
            headRow1.add("");
            headRow1.add("方差齐性p值");
            headRow1.add("t检验");
            headRow1.add("p");
            // 2. 第二行表头
            List<Object> headRow2 = new ArrayList<>();
            headRow2.add("");
            headRow2.add(finalGroupLabels.get(0));
            headRow2.add(finalGroupLabels.get(1));
            headRow2.add("");
            headRow2.add("");
            headRow2.add("");
            // 3. 数据区
            List<List<Object>> dataRows = new ArrayList<>();
            TTest tTest = new TTest();
            for (int idx = 0; idx < valueCols.size(); idx++) {
                Integer valueCol = valueCols.get(idx);
                List<Double> valueData = getNumericColumnData(sessionId, valueCol - 1);
                String title = (valueCol - 1 < excelHeaders.size() && excelHeaders.get(valueCol - 1) != null
                        && !excelHeaders.get(valueCol - 1).trim().isEmpty()) ? excelHeaders.get(valueCol - 1)
                                : ("题目" + valueCol);
                String rowTitle = title;
                List<Object> row = new ArrayList<>();
                row.add(rowTitle);
                // 分组数据
                List<Double> group1 = new ArrayList<>();
                List<Double> group2 = new ArrayList<>();
                for (Integer i : finalGroupRowIndices.get(0)) {
                    if (i < valueData.size() && valueData.get(i) != null)
                        group1.add(valueData.get(i));
                }
                for (Integer i : finalGroupRowIndices.get(1)) {
                    if (i < valueData.size() && valueData.get(i) != null)
                        group2.add(valueData.get(i));
                }
                DescriptiveStatistics stats1 = new DescriptiveStatistics();
                for (Double v : group1)
                    stats1.addValue(v);
                DescriptiveStatistics stats2 = new DescriptiveStatistics();
                for (Double v : group2)
                    stats2.addValue(v);
                row.add(String.format("%.2f±%.2f", stats1.getMean(), stats1.getStandardDeviation()));
                row.add(String.format("%.2f±%.2f", stats2.getMean(), stats2.getStandardDeviation()));
                // 方差齐性检验（F检验）
                double var1 = stats1.getVariance();
                double var2 = stats2.getVariance();
                int n1 = group1.size();
                int n2 = group2.size();
                double f = (var1 > var2 && var2 > 0) ? var1 / var2 : (var2 > 0 ? var2 / var1 : 1.0);
                double pLevene = 1.0;
                try {
                    org.apache.commons.math3.distribution.FDistribution fDist = new org.apache.commons.math3.distribution.FDistribution(
                            n1 - 1, n2 - 1);
                    pLevene = 2 * Math.min(fDist.cumulativeProbability(f), 1 - fDist.cumulativeProbability(f));
                } catch (Exception e) {
                }
                row.add(String.format("%.3f", pLevene));
                // t检验类型
                double t = Double.NaN, p = Double.NaN;
                String tType = "";
                try {
                    if (pLevene < 0.05) {
                        // 方差不齐，Welch t检验
                        t = tTest.t(group1.stream().mapToDouble(Double::doubleValue).toArray(),
                                group2.stream().mapToDouble(Double::doubleValue).toArray());
                        p = tTest.tTest(group1.stream().mapToDouble(Double::doubleValue).toArray(),
                                group2.stream().mapToDouble(Double::doubleValue).toArray());
                        tType = "t(不等方差)";
                    } else {
                        // 方差齐，等方差t检验
                        t = tTest.homoscedasticT(group1.stream().mapToDouble(Double::doubleValue).toArray(),
                                group2.stream().mapToDouble(Double::doubleValue).toArray());
                        p = tTest.homoscedasticTTest(group1.stream().mapToDouble(Double::doubleValue).toArray(),
                                group2.stream().mapToDouble(Double::doubleValue).toArray());
                        tType = "t(等方差)";
                    }
                } catch (Exception e) {
                }
                row.add(String.format("%.3f %s", t, tType));
                String pStr = String.format("%.3f", p);
                if (p < 0.01)
                    pStr += "**";
                else if (p < 0.05)
                    pStr += "*";
                row.add(pStr);
                dataRows.add(row);
            }
            // 4. 显著性说明行
            List<Object> ruleRow = new ArrayList<>();
            ruleRow.add("*p<0.05 **p<0.01，t(等方差)为方差齐性成立时的t检验，t(不等方差)为方差不齐时的Welch t检验");
            for (int i = 1; i < headRow2.size(); i++)
                ruleRow.add("");
            // 5. 合并单元格
            List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            cellMerges.add(newCellMerge(0, 1, 1, 2));
            cellMerges.add(newCellMerge(0, 0, 2, 1));
            cellMerges.add(newCellMerge(0, 3, 2, 1));
            cellMerges.add(newCellMerge(0, 4, 2, 1));
            cellMerges.add(newCellMerge(0, 5, 2, 1));
            cellMerges.add(newCellMerge(0, 6, 2, 1));
            cellMerges.add(newCellMerge(0, 7, 2, 1));
            cellMerges.add(newCellMerge(dataRows.size() + 2, 0, 1, 7));
            // 6. 单元格样式
            List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            List<FactorAnalysisOutput.CellStyle> styleRow1 = new ArrayList<>();
            for (int i = 0; i < 7; i++)
                styleRow1.add(newCellStyle("bold", null));
            cellStyles.add(styleRow1);
            List<FactorAnalysisOutput.CellStyle> styleRow2 = new ArrayList<>();
            for (int i = 0; i < 7; i++)
                styleRow2.add(newCellStyle("bold", null));
            cellStyles.add(styleRow2);
            for (List<Object> row : dataRows) {
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int i = 0; i < row.size(); i++) {
                    if (i == row.size() - 1 && row.get(i).toString().contains("*"))
                        styleRow.add(newCellStyle("bold", "#d62728"));
                    else
                        styleRow.add(newCellStyle(null, null));
                }
                cellStyles.add(styleRow);
            }
            List<FactorAnalysisOutput.CellStyle> ruleStyleRow = new ArrayList<>();
            for (int i = 0; i < 7; i++)
                ruleStyleRow.add(newCellStyle("bold", null));
            cellStyles.add(ruleStyleRow);
            // 7. 组装TableData
            List<List<Object>> allRows = new ArrayList<>();
            allRows.add(headRow1);
            allRows.add(headRow2);
            allRows.addAll(dataRows);
            allRows.add(ruleRow);
            for (List<Object> r : allRows) {
                while (r.size() < headRow2.size())
                    r.add("");
                if (r.size() > headRow2.size()) {
                    for (int i = r.size() - 1; i >= headRow2.size(); i--)
                        r.remove(i);
                }
            }
            FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
            table.setType("independent_ttest");
            table.setTitle("独立样本T检验");
            table.setRows(allRows);
            table.setCellMerges(cellMerges);
            table.setCellStyles(cellStyles);
            List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
            resultTables.add(table);
            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[performIndependentTTest] 独立样本T检验异常", e);
            throw new RuntimeException("独立样本T检验异常: " + e.getMessage());
        }
    }

    @Tool(description = "单样本t检验，输入题号列表和对比值，输出标准表格")
    public List<FactorAnalysisOutput.TableData> performOneSampleTTest(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "题号列表（从1开始，可多题）") List<Integer> questionNums,
            @ToolParam(description = "对比的数值") Double testValue) {
        log.info("[performOneSampleTTest] 单样本t检验 sessionId={}, questionNums={}, testValue={}", sessionId, questionNums,
                testValue);
        if (testValue == null) {
            throw new RuntimeException("请明确指定对比的数值（testValue），否则无法进行单样本t检验分析。");
        }
        List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
        org.apache.commons.math3.stat.inference.TTest tTest = new org.apache.commons.math3.stat.inference.TTest();
        // 表头
        List<String> headers = Arrays.asList("名称", "样本量", "最小值", "最大值", "平均值", "标准差", "t", "p");
        List<List<Object>> rows = new ArrayList<>();
        for (Integer questionNum : questionNums) {
            try {
                SurveyStructure struct = getSurveyStructureFromDatabase(sessionId, questionNum);
                if (struct == null || struct.getColIndices() == null || struct.getColIndices().isEmpty())
                    continue;
                String title = struct.getNumId() + "、" + struct.getTitle();
                int colIdx = struct.getColIndices().get(0) - 1;
                List<Double> data = getNumericColumnData(sessionId, colIdx);
                // 过滤无效数据
                List<Double> validData = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (validData.isEmpty())
                    continue;
                org.apache.commons.math3.stat.descriptive.DescriptiveStatistics stats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                for (Double v : validData)
                    stats.addValue(v);
                int n = (int) stats.getN();
                double min = stats.getMin();
                double max = stats.getMax();
                double mean = stats.getMean();
                double std = stats.getStandardDeviation();
                double[] arr = validData.stream().mapToDouble(Double::doubleValue).toArray();
                double t = tTest.t(testValue, arr);
                double p = tTest.tTest(testValue, arr);
                String pStr = p < 0.01 ? String.format("%.3f**", p)
                        : (p < 0.05 ? String.format("%.3f*", p) : String.format("%.3f", p));
                rows.add(Arrays.asList(
                        title, n, String.format("%.2f", min), String.format("%.2f", max),
                        String.format("%.2f", mean), String.format("%.3f", std),
                        String.format("%.3f", t), pStr));
            } catch (Exception e) {
                rows.add(Arrays.asList("题号 " + questionNum + " 分析失败", "-", "-", "-", "-", "-", "-", e.getMessage()));
            }
        }
        // 规则说明行，合并所有列，居中加粗红色
        int colCount = headers.size();
        List<Object> ruleRow = new ArrayList<>();
        ruleRow.add("*p<0.05 **p<0.01");
        for (int i = 1; i < colCount; i++)
            ruleRow.add("");
        rows.add(ruleRow);
        // 合并单元格
        List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
        cellMerges.add(newCellMerge(rows.size() - 1, 0, 1, colCount));
        // 单元格样式
        List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
            for (int j = 0; j < colCount; j++) {
                if (i == rows.size() - 1) {
                    styleRow.add(newCellStyle("bold", "#d62728"));
                } else {
                    styleRow.add(newCellStyle(null, null));
                }
            }
            cellStyles.add(styleRow);
        }
        FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
        table.setType("one_sample_ttest");
        table.setTitle("单样本t检验");
        table.setHeaders(headers);
        table.setRows(rows);
        table.setCellMerges(cellMerges);
        table.setCellStyles(cellStyles);
        resultTables.add(table);
        saveTableDataToMessage(sessionId, resultTables);
        return resultTables;
    }

    @Tool(description = "描述性统计分析，支持输入题号列表，输出包含样本量、最小值、最大值、平均值、标准差、中位数等描述性统计指标的表格。")
    public List<FactorAnalysisOutput.TableData> descriptiveStatistics(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "题号列表（从1开始，可多题）") List<Integer> questionNums) {
        log.info("[descriptiveStatistics] 描述性统计分析 sessionId={}, questionNums={}", sessionId, questionNums);
        List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
        List<String> headers = Arrays.asList("名称", "样本量", "最小值", "最大值", "平均值", "标准差", "中位数");
        List<List<Object>> rows = new ArrayList<>();
        for (Integer questionNum : questionNums) {
            try {
                SurveyStructure struct = getSurveyStructureFromDatabase(sessionId, questionNum);
                if (struct == null || struct.getColIndices() == null || struct.getColIndices().isEmpty())
                    continue;
                String type = struct.getType();
                // 多选题每个选项单独统计
                if ("4".equals(type) && struct.getOptions() != null) {
                    List<String> options = struct.getOptions();
                    List<Integer> colIndices = struct.getColIndices();
                    for (int i = 0; i < options.size(); i++) {
                        String optionName = options.get(i);
                        int colIdx = colIndices.get(i) - 1;
                        List<Double> data = getNumericColumnData(sessionId, colIdx);
                        List<Double> validData = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
                        if (validData.isEmpty())
                            continue;
                        org.apache.commons.math3.stat.descriptive.DescriptiveStatistics stats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                        for (Double v : validData)
                            stats.addValue(v);
                        int n = (int) stats.getN();
                        double min = stats.getMin();
                        double max = stats.getMax();
                        double mean = stats.getMean();
                        double std = stats.getStandardDeviation();
                        double median = stats.getPercentile(50);
                        rows.add(Arrays.asList(
                                struct.getNumId() + "." + struct.getTitle() + "-" + optionName,
                                n,
                                String.format("%.3f", min),
                                String.format("%.3f", max),
                                String.format("%.3f", mean),
                                String.format("%.3f", std),
                                String.format("%.3f", median)));
                    }
                    // 排序题每个选项单独统计
                } else if ("11".equals(type) && struct.getOptions() != null) {
                    List<String> options = struct.getOptions();
                    List<Integer> colIndices = struct.getColIndices();
                    for (int i = 0; i < options.size(); i++) {
                        String optionName = options.get(i);
                        int colIdx = colIndices.get(i) - 1;
                        List<Double> data = getNumericColumnData(sessionId, colIdx);
                        List<Double> validData = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
                        if (validData.isEmpty())
                            continue;
                        org.apache.commons.math3.stat.descriptive.DescriptiveStatistics stats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                        for (Double v : validData)
                            stats.addValue(v);
                        int n = (int) stats.getN();
                        double min = stats.getMin();
                        double max = stats.getMax();
                        double mean = stats.getMean();
                        double std = stats.getStandardDeviation();
                        double median = stats.getPercentile(50);
                        rows.add(Arrays.asList(
                                struct.getNumId() + "." + struct.getTitle() + "-" + optionName,
                                n,
                                String.format("%.3f", min),
                                String.format("%.3f", max),
                                String.format("%.3f", mean),
                                String.format("%.3f", std),
                                String.format("%.3f", median)));
                    }
                } else {
                    String title = struct.getNumId() + "、" + struct.getTitle();
                    int colIdx = struct.getColIndices().get(0) - 1;
                    List<Double> data = getNumericColumnData(sessionId, colIdx);
                    List<Double> validData = data.stream().filter(Objects::nonNull).collect(Collectors.toList());
                    if (validData.isEmpty())
                        continue;
                    org.apache.commons.math3.stat.descriptive.DescriptiveStatistics stats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                    for (Double v : validData)
                        stats.addValue(v);
                    int n = (int) stats.getN();
                    double min = stats.getMin();
                    double max = stats.getMax();
                    double mean = stats.getMean();
                    double std = stats.getStandardDeviation();
                    double median = stats.getPercentile(50);
                    rows.add(Arrays.asList(
                            title, n,
                            String.format("%.3f", min),
                            String.format("%.3f", max),
                            String.format("%.3f", mean),
                            String.format("%.3f", std),
                            String.format("%.3f", median)));
                }
            } catch (Exception e) {
                rows.add(Arrays.asList("题号 " + questionNum + " 分析失败", "-", "-", "-", "-", "-", e.getMessage()));
            }
        }
        FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
        table.setType("descriptive_statistics");
        table.setTitle("描述性统计分析");
        table.setHeaders(headers);
        table.setRows(rows);
        resultTables.add(table);
        saveTableDataToMessage(sessionId, resultTables);
        return resultTables;
    }

    /**
     * 调节效应分析工具
     * 
     * @param sessionId       会话ID
     * @param dependentVar    因变量列索引（从1开始，必须定量）
     * @param independentVars 自变量列索引列表（从1开始，可多个）
     * @param moderatorVars   调节变量列索引列表（从1开始，可多个）
     * @param centerType      处理方式 center=中心化，standard=标准化，none=不处理
     * @return 分层回归分析结果表格
     */
    @Tool(description = "调节效应分析，支持分层回归，自动中心化/标准化定量变量，支持定类变量哑变量处理，输出分层回归表格")
    public List<FactorAnalysisOutput.TableData> moderationEffectAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始，必须定量）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始，可多个）") List<Integer> independentVars,
            @ToolParam(description = "调节变量列索引列表（从1开始，可多个）") List<Integer> moderatorVars,
            @ToolParam(description = "定量变量处理方式 center=中心化，standard=标准化，none=不处理，默认center") String centerType,
            @ToolParam(description = "变量类型组合 quantitative_quantitative=X定量Z定量，quantitative_categorical=X定量Z定类，categorical_quantitative=X定类Z定量，默认quantitative_quantitative") String variableType) {
        log.info("[moderationEffectAnalysis] 调节效应分析 sessionId={}, Y={}, X={}, Z={}, centerType={}, variableType={}", sessionId,
                dependentVar, independentVars, moderatorVars, centerType, variableType);
        try {
            // 设置默认值
            if (variableType == null || variableType.isEmpty()) {
                variableType = "quantitative_quantitative";
            }

            // 1. 变量处理说明表
            List<List<Object>> varProcessRows = new ArrayList<>();
            varProcessRows.add(Arrays.asList("因变量", getColTitle(sessionId, dependentVar), "定量", "不处理"));

            // 根据变量类型组合设置处理说明
            for (Integer col : independentVars) {
                String xType = variableType.startsWith("quantitative") ? "定量" : "定类";
                String xProcess = variableType.startsWith("quantitative") ? "中心化" : "哑变量编码";
                varProcessRows.add(Arrays.asList("自变量", getColTitle(sessionId, col), xType, xProcess));

                // 如果是定类变量，添加参考组说明
                if (!variableType.startsWith("quantitative")) {
                    List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                    Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    if (!sortedValues.isEmpty()) {
                        varProcessRows.add(Arrays.asList("", getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]", "定类", "参照组"));
                    }
                }
            }
            for (Integer col : moderatorVars) {
                String zType = variableType.endsWith("quantitative") ? "定量" : "定类";
                String zProcess = variableType.endsWith("quantitative") ? "中心化" : "哑变量编码";
                varProcessRows.add(Arrays.asList("调节变量", getColTitle(sessionId, col), zType, zProcess));

                // 如果是定类变量，添加参考组说明
                if (!variableType.endsWith("quantitative")) {
                    List<String> zCatData = getCategoricalColumnData(sessionId, col - 1);
                    Set<String> uniqueValues = new LinkedHashSet<>(zCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    if (!sortedValues.isEmpty()) {
                        varProcessRows.add(Arrays.asList("", getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]", "定类", "参照组"));
                    }
                }
            }
            FactorAnalysisOutput.TableData varProcessTable = new FactorAnalysisOutput.TableData();
            varProcessTable.setType("moderation_var_process");
            varProcessTable.setTitle("研究变量处理说明");
            varProcessTable.setHeaders(Arrays.asList("类型", "名称", "数据类型", "数据处理"));
            varProcessTable.setRows(varProcessRows);

            // 2. 分层回归主表（严格对齐图片，三模型并列）
            // 获取数据
            List<Double> y = getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xList = new ArrayList<>();
            List<List<Double>> zList = new ArrayList<>();
            List<String> xVarNames = new ArrayList<>();
            List<String> zVarNames = new ArrayList<>();

            // 处理自变量X
            for (Integer col : independentVars) {
                if (variableType.startsWith("quantitative")) {
                    // X定量：直接获取数值数据
                    List<Double> xData = getNumericColumnData(sessionId, col - 1);
                    xList.add(xData);
                    xVarNames.add(getColTitle(sessionId, col));
                } else {
                    // X定类：进行哑变量编码
                    List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                    List<List<Double>> dummyVars = createDummyVariables(xCatData);
                    xList.addAll(dummyVars);
                    // 生成哑变量名称
                    Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    for (int i = 1; i < sortedValues.size(); i++) { // 从第二个开始，第一个作为参考组
                        xVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                    }
                }
            }

            // 处理调节变量Z
            for (Integer col : moderatorVars) {
                if (variableType.endsWith("quantitative")) {
                    // Z定量：直接获取数值数据
                    List<Double> zData = getNumericColumnData(sessionId, col - 1);
                    zList.add(zData);
                    zVarNames.add(getColTitle(sessionId, col));
                } else {
                    // Z定类：进行哑变量编码
                    List<String> zCatData = getCategoricalColumnData(sessionId, col - 1);
                    List<List<Double>> dummyVars = createDummyVariables(zCatData);
                    zList.addAll(dummyVars);
                    // 生成哑变量名称
                    Set<String> uniqueValues = new LinkedHashSet<>(zCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    for (int i = 1; i < sortedValues.size(); i++) { // 从第二个开始，第一个作为参考组
                        zVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                    }
                }
            }

            int n = y.size();

            // 对定量变量进行中心化处理
            if (variableType.startsWith("quantitative")) {
                for (List<Double> col : xList) {
                    double mean = col.stream().mapToDouble(Double::doubleValue).average().orElse(0);
                    for (int i = 0; i < col.size(); i++)
                        col.set(i, col.get(i) - mean);
                }
            }
            if (variableType.endsWith("quantitative")) {
                for (List<Double> col : zList) {
                    double mean = col.stream().mapToDouble(Double::doubleValue).average().orElse(0);
                    for (int i = 0; i < col.size(); i++)
                        col.set(i, col.get(i) - mean);
                }
            }
            // 构造交互项
            List<List<Double>> interactionList = new ArrayList<>();
            List<String> interactionNames = new ArrayList<>();
            for (int i = 0; i < xList.size(); i++) {
                for (int j = 0; j < zList.size(); j++) {
                    List<Double> xi = xList.get(i);
                    List<Double> zj = zList.get(j);
                    List<Double> inter = new ArrayList<>();
                    for (int k = 0; k < n; k++)
                        inter.add(xi.get(k) * zj.get(k));
                    interactionList.add(inter);
                    interactionNames.add(xVarNames.get(i) + "×" + zVarNames.get(j));
                }
            }
            // 三层模型变量
            List<List<List<Double>>> modelVarList = new ArrayList<>();
            modelVarList.add(xList);
            List<List<Double>> xzList = new ArrayList<>(xList);
            xzList.addAll(zList);
            modelVarList.add(xzList);
            List<List<Double>> xzzList = new ArrayList<>(xzList);
            xzzList.addAll(interactionList);
            modelVarList.add(xzzList);
            List<String> modelNames = Arrays.asList("模型1", "模型2", "模型3");
            List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
            resultTables.add(varProcessTable);
            // 分层回归主表（并列）
            // 先收集所有变量名，包括参照项
            List<List<String>> allVarNames = new ArrayList<>();
            int maxVarRows = 0;

            // 生成完整的变量名列表（包括参照项）
            List<String> fullVarNames = new ArrayList<>();
            fullVarNames.add("常数");

            // 添加X变量（包括参照项）
            if (!variableType.startsWith("quantitative")) {
                // X定类：添加参照项
                for (Integer col : independentVars) {
                    List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                    Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    if (!sortedValues.isEmpty()) {
                        fullVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                        for (int i = 1; i < sortedValues.size(); i++) {
                            fullVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                        }
                    }
                }
            } else {
                fullVarNames.addAll(xVarNames);
            }

            // 添加Z变量（包括参照项）
            if (!variableType.endsWith("quantitative")) {
                // Z定类：添加参照项
                for (Integer col : moderatorVars) {
                    List<String> zCatData = getCategoricalColumnData(sessionId, col - 1);
                    Set<String> uniqueValues = new LinkedHashSet<>(zCatData);
                    List<String> sortedValues = new ArrayList<>(uniqueValues);
                    Collections.sort(sortedValues);
                    if (!sortedValues.isEmpty()) {
                        fullVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                        for (int i = 1; i < sortedValues.size(); i++) {
                            fullVarNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                        }
                    }
                }
            } else {
                fullVarNames.addAll(zVarNames);
            }

            // 添加交互项
            fullVarNames.addAll(interactionNames);

            for (int m = 0; m < 3; m++) {
                List<String> varNames = new ArrayList<>();
                varNames.add("常数");
                if (m == 0) {
                    // 模型1：只有自变量X
                    if (!variableType.startsWith("quantitative")) {
                        // X定类：添加参照项和哑变量
                        for (Integer col : independentVars) {
                            List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                            Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                            List<String> sortedValues = new ArrayList<>(uniqueValues);
                            Collections.sort(sortedValues);
                            if (!sortedValues.isEmpty()) {
                                varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                                for (int i = 1; i < sortedValues.size(); i++) {
                                    varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                                }
                            }
                        }
                    } else {
                        varNames.addAll(xVarNames);
                    }
                }
                if (m == 1) {
                    // 模型2：自变量X + 调节变量Z
                    if (!variableType.startsWith("quantitative")) {
                        // X定类：添加参照项和哑变量
                        for (Integer col : independentVars) {
                            List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                            Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                            List<String> sortedValues = new ArrayList<>(uniqueValues);
                            Collections.sort(sortedValues);
                            if (!sortedValues.isEmpty()) {
                                varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                                for (int i = 1; i < sortedValues.size(); i++) {
                                    varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                                }
                            }
                        }
                    } else {
                        varNames.addAll(xVarNames);
                    }

                    if (!variableType.endsWith("quantitative")) {
                        // Z定类：添加参照项和哑变量
                        for (Integer col : moderatorVars) {
                            List<String> zCatData = getCategoricalColumnData(sessionId, col - 1);
                            Set<String> uniqueValues = new LinkedHashSet<>(zCatData);
                            List<String> sortedValues = new ArrayList<>(uniqueValues);
                            Collections.sort(sortedValues);
                            if (!sortedValues.isEmpty()) {
                                varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                                for (int i = 1; i < sortedValues.size(); i++) {
                                    varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                                }
                            }
                        }
                    } else {
                        varNames.addAll(zVarNames);
                    }
                }
                if (m == 2) {
                    // 模型3：自变量X + 调节变量Z + 交互项
                    if (!variableType.startsWith("quantitative")) {
                        // X定类：添加参照项和哑变量
                        for (Integer col : independentVars) {
                            List<String> xCatData = getCategoricalColumnData(sessionId, col - 1);
                            Set<String> uniqueValues = new LinkedHashSet<>(xCatData);
                            List<String> sortedValues = new ArrayList<>(uniqueValues);
                            Collections.sort(sortedValues);
                            if (!sortedValues.isEmpty()) {
                                varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                                for (int i = 1; i < sortedValues.size(); i++) {
                                    varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                                }
                            }
                        }
                    } else {
                        varNames.addAll(xVarNames);
                    }

                    if (!variableType.endsWith("quantitative")) {
                        // Z定类：添加参照项和哑变量
                        for (Integer col : moderatorVars) {
                            List<String> zCatData = getCategoricalColumnData(sessionId, col - 1);
                            Set<String> uniqueValues = new LinkedHashSet<>(zCatData);
                            List<String> sortedValues = new ArrayList<>(uniqueValues);
                            Collections.sort(sortedValues);
                            if (!sortedValues.isEmpty()) {
                                varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(0) + "[参照项]");
                                for (int i = 1; i < sortedValues.size(); i++) {
                                    varNames.add(getColTitle(sessionId, col) + "-" + sortedValues.get(i));
                                }
                            }
                        }
                    } else {
                        varNames.addAll(zVarNames);
                    }

                    varNames.addAll(interactionNames);
                }
                allVarNames.add(varNames);
                maxVarRows = Math.max(maxVarRows, varNames.size());
            }
            // 每个模型的回归结果
            List<List<List<Object>>> allModelRows = new ArrayList<>();
            List<Double> r2List = new ArrayList<>();
            List<Double> adjR2List = new ArrayList<>();
            List<Double> fList = new ArrayList<>();
            List<Double> deltaR2List = new ArrayList<>();
            List<Double> deltaFList = new ArrayList<>();
            double lastR2 = 0.0;
            for (int m = 0; m < 3; m++) {
                List<List<Double>> curX = modelVarList.get(m);
                int k = curX.size();
                double[][] X = new double[n][k];
                for (int i = 0; i < n; i++)
                    for (int j = 0; j < k; j++)
                        X[i][j] = curX.get(j).get(i);
                double[] Y = new double[n];
                for (int i = 0; i < n; i++)
                    Y[i] = y.get(i);
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regression = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regression.newSampleData(Y, X);
                double[] beta = regression.estimateRegressionParameters();
                double[] stdErr = regression.estimateRegressionParametersStandardErrors();
                double[] tVals = new double[beta.length];
                double[] pVals = new double[beta.length];
                double[] stdBeta = new double[beta.length];
                double yStd = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics(Y)
                        .getStandardDeviation();
                for (int j = 1; j < beta.length; j++) {
                    org.apache.commons.math3.stat.descriptive.DescriptiveStatistics xStats = new org.apache.commons.math3.stat.descriptive.DescriptiveStatistics();
                    for (int i = 0; i < n; i++)
                        xStats.addValue(X[i][j - 1]);
                    double xStd = xStats.getStandardDeviation();
                    stdBeta[j] = beta[j] * xStd / yStd;
                }
                stdBeta[0] = Double.NaN;
                org.apache.commons.math3.distribution.TDistribution tDist = new org.apache.commons.math3.distribution.TDistribution(
                        n - k - 1);
                for (int j = 0; j < beta.length; j++) {
                    tVals[j] = beta[j] / stdErr[j];
                    pVals[j] = 2 * (1 - tDist.cumulativeProbability(Math.abs(tVals[j])));
                }
                // 变量名
                List<String> varNames = allVarNames.get(m);
                // 表格
                List<List<Object>> rows = new ArrayList<>();
                for (int i = 0; i < maxVarRows; i++) {
                    List<Object> row = new ArrayList<>();
                    if (i < varNames.size()) {
                        String varName = varNames.get(i);
                        row.add(varName);

                        // 如果是参照项，显示空值
                        if (varName.contains("[参照项]")) {
                            row.add("-");
                            row.add("-");
                            row.add("-");
                            row.add("-");
                            row.add("-");
                        } else {
                            // 计算实际的回归系数索引（跳过参照项）
                            int actualIndex = i;
                            for (int j = 0; j < i; j++) {
                                if (varNames.get(j).contains("[参照项]")) {
                                    actualIndex--;
                                }
                            }

                            row.add(String.format("%.3f", actualIndex < beta.length ? beta[actualIndex] : Double.NaN));
                            row.add(String.format("%.3f", actualIndex < stdErr.length ? stdErr[actualIndex] : Double.NaN));
                            row.add(String.format("%.3f", actualIndex < tVals.length ? tVals[actualIndex] : Double.NaN));
                            String sig = (actualIndex < pVals.length && pVals[actualIndex] < 0.01) ? "**"
                                    : ((actualIndex < pVals.length && pVals[actualIndex] < 0.05) ? "*" : "");
                            row.add((actualIndex < pVals.length ? (pVals[actualIndex] < 0.001 ? "0.000" : String.format("%.3f", pVals[actualIndex])) : "")
                                    + sig);
                            row.add(actualIndex == 0 ? "-" : (actualIndex < stdBeta.length ? String.format("%.3f", stdBeta[actualIndex]) : ""));
                        }
                    } else {
                        for (int j = 0; j < 6; j++)
                            row.add("");
                    }
                    rows.add(row);
                }
                double r2 = regression.calculateRSquared();
                double adjR2 = regression.calculateAdjustedRSquared();
                double ess = regression.calculateTotalSumOfSquares() - regression.calculateResidualSumOfSquares();
                double rss = regression.calculateResidualSumOfSquares();
                double f = ((ess / k) / (rss / (n - k - 1)));
                double deltaR2 = m == 0 ? r2 : r2 - lastR2;
                // 计算ΔF值
                double deltaF;
                int deltaDF;
                if (m == 0) {
                    deltaF = f;
                    deltaDF = k;
                } else if (m == 1) {
                    // 模型2相对于模型1的增量F检验
                    // 新增的是Z变量的数量
                    int newVars = zVarNames.size(); // Z变量的数量
                    deltaF = ((r2 - lastR2) * (n - k - 1)) / ((1 - r2) * newVars);
                    deltaDF = newVars;
                    System.out.println("模型2 ΔF计算: variableType=" + variableType + ", deltaR2=" + (r2-lastR2) + ", newVars=" + newVars + ", deltaF=" + deltaF);
                } else {
                    // 模型3相对于模型2的增量F检验
                    // 新增的是交互项数量
                    int newVars = interactionNames.size();
                    deltaF = ((r2 - lastR2) * (n - k - 1)) / ((1 - r2) * newVars);
                    deltaDF = newVars;
                    System.out.println("模型3 ΔF计算: deltaR2=" + (r2-lastR2) + ", newVars=" + newVars + ", deltaF=" + deltaF);
                }
                r2List.add(r2);
                adjR2List.add(adjR2);
                fList.add(f);
                deltaR2List.add(deltaR2);
                deltaFList.add(deltaF);
                lastR2 = r2;
                // 统计指标行
                rows.add(Arrays.asList("R²", String.format("%.3f", r2), "", "", "", ""));
                rows.add(Arrays.asList("调整R²", String.format("%.3f", adjR2), "", "", "", ""));
                rows.add(Arrays.asList("F值", String.format("F (%d,%d)=%.3f,p=0.000", k, n - k - 1, f), "", "", "", ""));
                rows.add(Arrays.asList("ΔR²", String.format("%.3f", deltaR2), "", "", "", ""));
                // 计算ΔF的p值和分母自由度
                int denominatorDF = n - k - 1;

                // 使用F分布计算p值
                double deltaFPValue;
                try {
                    FDistribution fDist = new FDistribution(deltaDF, denominatorDF);
                    deltaFPValue = 1.0 - fDist.cumulativeProbability(deltaF);
                    if (deltaFPValue < 0.001) {
                        deltaFPValue = 0.000;
                    }
                } catch (Exception e) {
                    deltaFPValue = 0.000; // 默认值
                }

                System.out.println("模型" + (m+1) + " ΔF显示: deltaDF=" + deltaDF + ", denominatorDF=" + denominatorDF + ", deltaF=" + deltaF + ", pValue=" + deltaFPValue);

                String deltaFStr = String.format("F (%d,%d)=%.3f,p=%.3f", deltaDF, denominatorDF, deltaF, deltaFPValue);
                System.out.println("模型" + (m+1) + " 存储的ΔF值: " + deltaFStr);
                rows.add(Arrays.asList("ΔF值", deltaFStr, "", "", "", ""));
                allModelRows.add(rows);
            }
            // 合并三模型为一个大表格
            List<List<Object>> mergedRows = new ArrayList<>();
            // 表头
            List<Object> headRow1 = new ArrayList<>();
            headRow1.add("");
            for (int m = 0; m < 3; m++)
                for (int i = 0; i < 5; i++)
                    headRow1.add(modelNames.get(m)); // 5列：B, 标准误, t, p, β
            mergedRows.add(headRow1);
            List<Object> headRow2 = new ArrayList<>();
            headRow2.add("");
            for (int m = 0; m < 3; m++)
                headRow2.addAll(Arrays.asList("B", "标准误", "t", "p", "β"));
            mergedRows.add(headRow2);
            // 变量行
            for (int i = 0; i < maxVarRows; i++) {
                List<Object> row = new ArrayList<>();
                row.add(i < allVarNames.get(2).size() ? allVarNames.get(2).get(i) : ""); // 以模型3变量名为主
                for (int m = 0; m < 3; m++) {
                    List<List<Object>> modelRows = allModelRows.get(m);
                    if (i < modelRows.size() - 5)
                        row.addAll(modelRows.get(i).subList(1, 6));
                    else
                        for (int j = 0; j < 5; j++)
                            row.add("");
                }
                mergedRows.add(row);
            }
            // 统计指标行（R²等）
            String[] statNames = { "R²", "调整R²", "F值", "ΔR²", "ΔF值" };
            // 这里不需要重新计算ΔF值，因为已经在上面正确计算过了
            // 直接从allModelRows中提取ΔF值字符串
            for (int s = 0; s < 5; s++) {
                List<Object> row = new ArrayList<>();
                row.add(statNames[s]);
                for (int m = 0; m < 3; m++) {
                    List<List<Object>> modelRows = allModelRows.get(m);
                    int idx = modelRows.size() - 5 + s;
                    // 直接从modelRows中提取统计指标，包括ΔF值
                    List<Object> statRow = modelRows.get(idx);
                    if (s == 4) { // ΔF值行
                        System.out.println("模型" + (m+1) + " 合并时提取的ΔF值: " + statRow.get(1));
                    }
                    row.addAll(statRow.subList(1, 6));
                }
                mergedRows.add(row);
            }
            // 备注行
            int totalCols = 1 + 3 * 5;
            String depVarTitleMain = getColTitle(sessionId, dependentVar);
            mergedRows.add(Arrays.asList("备注：因变量 = " + dependentVar + "，" + depVarTitleMain
                    + new String(new char[totalCols - 1]).replace("\0", "")));
            // 星号说明
            mergedRows.add(Arrays.asList("*p<0.05 **p<0.01" + new String(new char[totalCols - 1]).replace("\0", "")));
            // 合并单元格
            List<com.example.springboot.entity.FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            // 一级表头合并
            int col = 1;
            for (int m = 0; m < 3; m++) {
                cellMerges.add(newCellMerge(0, col, 1, 5));
                col += 5;
            }
            // 备注、星号说明行合并
            cellMerges.add(newCellMerge(mergedRows.size() - 2, 0, 1, totalCols));
            cellMerges.add(newCellMerge(mergedRows.size() - 1, 0, 1, totalCols));
            // 统计量行每个模型合并5列
            for (int s = 0; s < 5; s++) {
                int rowIdx = 2 + maxVarRows + s;
                col = 1;
                for (int m = 0; m < 3; m++) {
                    cellMerges.add(newCellMerge(rowIdx, col, 1, 5));
                    col += 5;
                }
            }
            // 居中样式
            List<List<com.example.springboot.entity.FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            for (int i = 0; i < mergedRows.size(); i++) {
                List<com.example.springboot.entity.FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int j = 0; j < totalCols; j++) {
                    if (i == 0 || i == 1 || i >= mergedRows.size() - 2)
                        styleRow.add(newCellStyle("bold", null));
                    else
                        styleRow.add(newCellStyle(null, null));
                }
                cellStyles.add(styleRow);
            }
            FactorAnalysisOutput.TableData mergedTable = new FactorAnalysisOutput.TableData();
            mergedTable.setType("moderation_regression");
            mergedTable.setTitle("调节效应分析结果 (n=" + n + ")");
            mergedTable.setRows(mergedRows);
            mergedTable.setCellMerges(cellMerges);
            mergedTable.setCellStyles(cellStyles);
            resultTables.add(mergedTable);
            // 3. 简化格式表格（严格如图片）
            List<List<Object>> simpleRows = new ArrayList<>();
            // 表头
            List<Object> simpleHead = new ArrayList<>();
            simpleHead.add("");
            simpleHead.add("模型1");
            simpleHead.add("模型2");
            simpleHead.add("模型3");
            simpleRows.add(simpleHead);
            // 变量名行（以模型3变量名为主）
            List<String> simpleVarNames = allVarNames.get(2);
            int maxSimpleRows = simpleVarNames.size();
            for (int i = 0; i < maxSimpleRows; i++) {
                List<Object> row = new ArrayList<>();
                row.add(simpleVarNames.get(i));
                for (int m = 0; m < 3; m++) {
                    List<List<Object>> modelRows = allModelRows.get(m);
                    if (i < modelRows.size() - 5) {
                        // 回归系数（带星号，括号内t值）
                        String b = modelRows.get(i).get(1).toString();
                        String t = modelRows.get(i).get(3).toString();
                        String p = modelRows.get(i).get(4).toString();
                        String sig = "";
                        if (p.contains("**"))
                            sig = "**";
                        else if (p.contains("*"))
                            sig = "*";
                        String val = "";
                        if (b != null && !b.isEmpty() && !b.equals("NaN") && t != null && !t.isEmpty()
                                && !t.equals("NaN")) {
                            val = b + sig + " (" + t + ")";
                        } else if (b != null && !b.isEmpty() && !b.equals("NaN")) {
                            val = b + sig;
                        } // 否则为空
                        row.add(val);
                    } else {
                        row.add("");
                    }
                }
                simpleRows.add(row);
            }
            // 统计量行
            String[] simpleStatNames = { "样本量", "R²", "调整R²", "F值", "ΔR²", "ΔF值" };
            for (int s = 0; s < 6; s++) {
                List<Object> row = new ArrayList<>();
                row.add(simpleStatNames[s]);
                for (int m = 0; m < 3; m++) {
                    if (s == 0) {
                        row.add(n);
                    } else if (s == 5) { // ΔF值
                        List<List<Object>> modelRows = allModelRows.get(m);
                        int deltaFIdx = modelRows.size() - 1; // ΔF值是最后一行
                        row.add(modelRows.get(deltaFIdx).get(1));
                    } else {
                        List<List<Object>> modelRows = allModelRows.get(m);
                        int idx = modelRows.size() - 5 + (s - 1);
                        if (idx >= 0 && idx < modelRows.size()) {
                            row.add(modelRows.get(idx).get(1));
                        } else {
                            row.add("");
                        }
                    }
                }
                simpleRows.add(row);
            }
            // 备注行
            String depVarTitle = getColTitle(sessionId, dependentVar);
            simpleRows.add(Arrays.asList("备注：因变量 = " + dependentVar + "，" + depVarTitle, "", "", ""));
            // 星号说明
            simpleRows.add(Arrays.asList("* p<0.05 ** p<0.01 指号里面为t值", "", "", ""));
            // 简化表格合并单元格
            List<com.example.springboot.entity.FactorAnalysisOutput.CellMerge> simpleCellMerges = new ArrayList<>();
            // 表头合并
            for (int m = 1; m <= 3; m++) {
                simpleCellMerges.add(newCellMerge(0, m, 1, 1));
            }
            // 备注、星号说明行合并
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 2, 0, 1, 4));
            simpleCellMerges.add(newCellMerge(simpleRows.size() - 1, 0, 1, 4));
            // 居中样式
            List<List<com.example.springboot.entity.FactorAnalysisOutput.CellStyle>> simpleCellStyles = new ArrayList<>();
            for (int i = 0; i < simpleRows.size(); i++) {
                List<com.example.springboot.entity.FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int j = 0; j < 4; j++) {
                    if (i == 0 || i >= simpleRows.size() - 2)
                        styleRow.add(newCellStyle("bold", null));
                    else
                        styleRow.add(newCellStyle(null, null));
                }
                simpleCellStyles.add(styleRow);
            }
            FactorAnalysisOutput.TableData simpleTable = new FactorAnalysisOutput.TableData();
            simpleTable.setType("moderation_simple");
            simpleTable.setTitle("调节效应分析结果-简化格式");
            simpleTable.setRows(simpleRows);
            simpleTable.setCellMerges(simpleCellMerges);
            simpleTable.setCellStyles(simpleCellStyles);
            resultTables.add(simpleTable);
            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[moderationEffectAnalysis] 调节效应分析异常", e);
            throw new RuntimeException("调节效应分析异常: " + e.getMessage());
        }
    }

    // 判断某列是否为定量（数值型）
    protected boolean isNumericColumn(String sessionId, int colIdx) {
        List<String> data = getColumnData(sessionId, colIdx);
        int numericCount = 0, total = 0;
        for (String v : data) {
            if (v == null || v.trim().isEmpty())
                continue;
            try {
                Double.parseDouble(v);
                numericCount++;
            } catch (Exception e) {
            }
            total++;
        }
        return total > 0 && numericCount * 1.0 / total > 0.8;
    }

    // 获取列标题
    protected String getColTitle(String sessionId, int colIdx) {
        try {
            AiChatMessage message = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (message != null && message.getCompleteExcelData() != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                List<List<String>> excelData = objectMapper.readValue(message.getCompleteExcelData(),
                        new com.fasterxml.jackson.core.type.TypeReference<List<List<String>>>() {
                        });
                List<String> headers = excelData.get(0);
                if (colIdx - 1 < headers.size())
                    return headers.get(colIdx - 1);
            }
        } catch (Exception e) {
        }
        return "变量" + colIdx;
    }

    /**
     * 中介效应分析工具，支持平行中介和链式中介
     *
     * @param sessionId       会话ID
     * @param dependentVar    因变量列索引（从1开始）
     * @param independentVars 自变量列索引列表（从1开始）
     * @param mediatorVars    中介变量列索引列表（从1开始）
     * @param mediationType   中介类型，parallel=平行中介，chain=链式中介，默认parallel
     * @return 中介效应分析结果表格
     */
    @Tool(description = "中介效应分析，支持平行和链式中介，自动生成标准表格")
    public List<FactorAnalysisOutput.TableData> mediationEffectAnalysis(
            @ToolParam(description = "会话ID") String sessionId,
            @ToolParam(description = "因变量列索引（从1开始）") Integer dependentVar,
            @ToolParam(description = "自变量列索引列表（从1开始，可多个）") List<Integer> independentVars,
            @ToolParam(description = "中介变量列索引列表（从1开始，可多个）") List<Integer> mediatorVars,
            @ToolParam(description = "中介类型，parallel=平行中介，chain=链式中介，默认parallel") String mediationType) {
        log.info("[mediationEffectAnalysis] sessionId={}, Y={}, X={}, M={}, type={}", sessionId, dependentVar,
                independentVars, mediatorVars, mediationType);
        try {
            // 1. 获取数据
            List<Double> y = getNumericColumnData(sessionId, dependentVar - 1);
            List<List<Double>> xList = new ArrayList<>();
            for (Integer idx : independentVars)
                xList.add(getNumericColumnData(sessionId, idx - 1));
            List<List<Double>> mList = new ArrayList<>();
            for (Integer idx : mediatorVars)
                mList.add(getNumericColumnData(sessionId, idx - 1));
            int n = y.size();

            // 2. 数据对齐（去除缺失）
            List<Integer> validIdx = new ArrayList<>();
            for (int i = 0; i < n; i++) {
                boolean valid = y.get(i) != null;
                for (List<Double> x : xList)
                    valid &= (i < x.size() && x.get(i) != null);
                for (List<Double> m : mList)
                    valid &= (i < m.size() && m.get(i) != null);
                if (valid)
                    validIdx.add(i);
            }
            List<Double> yv = new ArrayList<>();
            List<List<Double>> xv = new ArrayList<>();
            List<List<Double>> mv = new ArrayList<>();
            for (List<Double> x : xList)
                xv.add(new ArrayList<>());
            for (List<Double> m : mList)
                mv.add(new ArrayList<>());
            for (int i : validIdx) {
                yv.add(y.get(i));
                for (int j = 0; j < xList.size(); j++)
                    xv.get(j).add(xList.get(j).get(i));
                for (int j = 0; j < mList.size(); j++)
                    mv.get(j).add(mList.get(j).get(i));
            }
            int N = yv.size();
            // 3. 路径回归分析
            // 根据中介类型选择不同的分析方法
            if (mediationType == null) mediationType = "parallel";

            List<Object[]> resultRows = new ArrayList<>();
            List<Object[]> ciRows = new ArrayList<>();
            List<Object[]> effectRows = new ArrayList<>();
            List<Object[]> summaryRows = new ArrayList<>();
            double c = 0, c_ = 0;
            double[] a = new double[mv.size()];
            double[] b = new double[mv.size()];
            double[] ab = new double[mv.size()];
            double[] ab_se = new double[mv.size()];
            double[] ab_z = new double[mv.size()];
            double[] ab_p = new double[mv.size()];
            double[] ab_ci_low = new double[mv.size()];
            double[] ab_ci_high = new double[mv.size()];
            double[] c_ci = new double[2];
            double[] c__ci = new double[2];
            int xDim = xv.size();

            if ("chain".equals(mediationType)) {
                // 链式中介分析
                performChainMediationAnalysis(yv, xv, mv, N, xDim, a, b, ab, ab_se, ab_z, ab_p, ab_ci_low, ab_ci_high);
            } else {
                // 平行中介分析（默认）
                performParallelMediationAnalysis(yv, xv, mv, N, xDim, a, b, ab, ab_se, ab_z, ab_p, ab_ci_low, ab_ci_high);
            }

            // 计算总效应 c (X->Y)
            double[][] XmatY = new double[N][xDim];
            for (int j = 0; j < N; j++)
                for (int d = 0; d < xDim; d++)
                    XmatY[j][d] = xv.get(d).get(j);
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXY = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            double[] Y = new double[N];
            for (int j = 0; j < N; j++)
                Y[j] = yv.get(j);
            regXY.newSampleData(Y, XmatY);
            double[] betaXY = regXY.estimateRegressionParameters();
            c = betaXY[1]; // 取第一个自变量的系数

            // 计算直接效应 c' (X,M->Y)
            double[][] XM = new double[N][xDim + mv.size()];
            for (int j = 0; j < N; j++) {
                for (int d = 0; d < xDim; d++)
                    XM[j][d] = xv.get(d).get(j);
                for (int k = 0; k < mv.size(); k++)
                    XM[j][xDim + k] = mv.get(k).get(j);
            }
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXMY = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            regXMY.newSampleData(Y, XM);
            double[] betaXMY = regXMY.estimateRegressionParameters();
            c_ = betaXMY[1]; // 取第一个自变量的系数
            // 4. 间接效应a*b，Bootstrap置信区间
            int bootN = 1000;
            double[][] abBoot = new double[mv.size()][bootN];
            java.util.Random rand = new java.util.Random();
            for (int bidx = 0; bidx < bootN; bidx++) {
                int[] idxs = new int[N];
                for (int i = 0; i < N; i++)
                    idxs[i] = rand.nextInt(N);
                // X->M
                double[] a_b = new double[mv.size()];
                for (int i = 0; i < mv.size(); i++) {
                    double[] Mb = new double[N];
                    double[][] Xmatb = new double[N][xDim];
                    for (int j = 0; j < N; j++) {
                        Mb[j] = mv.get(i).get(idxs[j]);
                        for (int d = 0; d < xDim; d++)
                            Xmatb[j][d] = xv.get(d).get(idxs[j]);
                    }
                    org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXM = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                    regXM.newSampleData(Mb, Xmatb);
                    double[] beta = regXM.estimateRegressionParameters();
                    a_b[i] = beta[1];
                }
                // X,M->Y
                double[][] XMb = new double[N][xDim + mv.size()];
                for (int j = 0; j < N; j++) {
                    for (int d = 0; d < xDim; d++)
                        XMb[j][d] = xv.get(d).get(idxs[j]);
                    for (int k = 0; k < mv.size(); k++)
                        XMb[j][xDim + k] = mv.get(k).get(idxs[j]);
                }
                double[] Yb = new double[N];
                for (int j = 0; j < N; j++)
                    Yb[j] = yv.get(idxs[j]);
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXMYb = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regXMYb.newSampleData(Yb, XMb);
                double[] betaXMYb = regXMYb.estimateRegressionParameters();
                for (int k = 0; k < mv.size(); k++) {
                    abBoot[k][bidx] = a_b[k] * betaXMYb[xDim + k + 1];
                }
            }
            for (int k = 0; k < mv.size(); k++) {
                ab[k] = a[k] * b[k];
                double[] boots = abBoot[k];
                java.util.Arrays.sort(boots);
                ab_ci_low[k] = boots[(int) (bootN * 0.025)];
                ab_ci_high[k] = boots[(int) (bootN * 0.975)];
                ab_se[k] = std(boots);
                ab_z[k] = ab[k] / (ab_se[k] + 1e-10);
                ab_p[k] = 2 * (1 - new org.apache.commons.math3.distribution.NormalDistribution()
                        .cumulativeProbability(Math.abs(ab_z[k])));
            }
            // 5. 组装标准中介分析表格
            // 获取变量名称
            String yName = getColTitle(sessionId, dependentVar);
            List<String> xNames = new ArrayList<>();
            for (Integer idx : independentVars) {
                xNames.add(getColTitle(sessionId, idx));
            }
            List<String> mNames = new ArrayList<>();
            for (Integer idx : mediatorVars) {
                mNames.add(getColTitle(sessionId, idx));
            }

            // 构建表格数据
            List<List<Object>> mergedRows = new ArrayList<>();

            // 一级表头 - 根据图片格式调整
            List<Object> headRow1 = new ArrayList<>();
            headRow1.add("");
            // 模型1: Y ~ X (总效应)
            headRow1.add("1. " + yName);
            for (int i = 1; i < 5; i++) headRow1.add("");
            // 模型2: M ~ X (a路径) - 为每个中介变量创建一个模型
            for (int m = 0; m < mediatorVars.size(); m++) {
                headRow1.add((m + 2) + ". " + mNames.get(m));
                for (int i = 1; i < 5; i++) headRow1.add("");
            }
            // 最后一个模型: Y ~ X + M (直接效应和b路径)
            headRow1.add((2 + mediatorVars.size()) + ". " + yName);
            for (int i = 1; i < 5; i++) headRow1.add("");
            mergedRows.add(headRow1);

            // 二级表头
            List<Object> headRow2 = new ArrayList<>();
            headRow2.add("");
            int totalModels = 2 + mediatorVars.size();
            for (int m = 0; m < totalModels; m++) {
                headRow2.addAll(Arrays.asList("B", "标准误", "t", "p", "β"));
            }
            mergedRows.add(headRow2);

            // 根据图片格式，需要为每个变量创建行，但只在相应模型中显示数据
            // 变量行结构：常数、自变量、中介变量
            List<String> allVarNames = new ArrayList<>();
            allVarNames.add("常数");
            allVarNames.addAll(xNames);
            allVarNames.addAll(mNames);

            // 存储所有模型的回归结果
            List<double[]> allBetas = new ArrayList<>();
            List<double[]> allSEs = new ArrayList<>();
            List<double[]> allTs = new ArrayList<>();
            List<double[]> allPs = new ArrayList<>();
            List<double[]> allStdBetas = new ArrayList<>();
            List<Double> allR2s = new ArrayList<>();
            List<Double> allAdjR2s = new ArrayList<>();
            List<String> allFStats = new ArrayList<>();

            try {
                double[] yArray = yv.stream().mapToDouble(Double::doubleValue).toArray();
                Double yStdObj = calculateStd(yv);
                double yStd = yStdObj != null ? yStdObj : 1.0;

                // 模型1: Y ~ X (总效应)
                double[][] xMatrix1 = new double[N][xDim];
                for (int i = 0; i < N; i++) {
                    for (int j = 0; j < xDim; j++) {
                        xMatrix1[i][j] = xv.get(j).get(i);
                    }
                }
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression reg1 = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                reg1.newSampleData(yArray, xMatrix1);
                double[] beta1 = reg1.estimateRegressionParameters();
                double[] se1 = reg1.estimateRegressionParametersStandardErrors();
                double r2_1 = reg1.calculateRSquared();
                double adjR2_1 = reg1.calculateAdjustedRSquared();

                // 计算t值、p值和标准化系数
                org.apache.commons.math3.distribution.TDistribution tDist1 = new org.apache.commons.math3.distribution.TDistribution(N - xDim - 1);
                double[] t1 = new double[beta1.length];
                double[] p1 = new double[beta1.length];
                double[] stdBeta1 = new double[beta1.length];
                stdBeta1[0] = Double.NaN; // 常数项

                for (int i = 0; i < beta1.length; i++) {
                    t1[i] = beta1[i] / se1[i];
                    p1[i] = 2 * (1 - tDist1.cumulativeProbability(Math.abs(t1[i])));
                    if (i > 0) {
                        Double xStdObj = calculateStd(xv.get(i - 1));
                        double xStd = xStdObj != null ? xStdObj : 1.0;
                        stdBeta1[i] = beta1[i] * xStd / yStd;
                    }
                }

                // 计算F统计量
                double mse1 = reg1.calculateResidualSumOfSquares() / (N - xDim - 1);
                double msr1 = (reg1.calculateTotalSumOfSquares() - reg1.calculateResidualSumOfSquares()) / xDim;
                double fStat1 = msr1 / mse1;
                org.apache.commons.math3.distribution.FDistribution fDist1 = new org.apache.commons.math3.distribution.FDistribution(xDim, N - xDim - 1);
                double fP1 = 1 - fDist1.cumulativeProbability(fStat1);

                allBetas.add(beta1);
                allSEs.add(se1);
                allTs.add(t1);
                allPs.add(p1);
                allStdBetas.add(stdBeta1);
                allR2s.add(r2_1);
                allAdjR2s.add(adjR2_1);
                allFStats.add(String.format("F(%d,%d)=%.3f,p=%.3f", xDim, N-xDim-1, fStat1, fP1));

                // 模型2-n: M ~ X (a路径，每个中介变量一个模型)
                for (int m = 0; m < mediatorVars.size(); m++) {
                    double[] mArray = mv.get(m).stream().mapToDouble(Double::doubleValue).toArray();

                    if ("chain".equals(mediationType)) {
                        // 链式中介：构建预测变量矩阵，包含X和前面的中介变量
                        int numPredictors = xDim + m; // X变量 + 前m个中介变量
                        double[][] predictorMatrix = new double[N][numPredictors];

                        // 添加X变量
                        for (int i = 0; i < N; i++) {
                            for (int j = 0; j < xDim; j++) {
                                predictorMatrix[i][j] = xv.get(j).get(i);
                            }
                        }

                        // 添加前面的中介变量
                        for (int i = 0; i < N; i++) {
                            for (int j = 0; j < m; j++) {
                                predictorMatrix[i][xDim + j] = mv.get(j).get(i);
                            }
                        }

                        org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regM = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                        regM.newSampleData(mArray, predictorMatrix);
                        double[] betaM = regM.estimateRegressionParameters();
                        double[] seM = regM.estimateRegressionParametersStandardErrors();
                        double r2_M = regM.calculateRSquared();
                        double adjR2_M = regM.calculateAdjustedRSquared();

                        // 计算t值、p值和标准化系数
                        int dfM = N - numPredictors - 1;
                        org.apache.commons.math3.distribution.TDistribution tDistM = new org.apache.commons.math3.distribution.TDistribution(dfM);
                        double[] tM = new double[betaM.length];
                        double[] pM = new double[betaM.length];
                        double[] stdBetaM = new double[betaM.length];
                        stdBetaM[0] = Double.NaN; // 常数项

                        Double mStdObj = calculateStd(mv.get(m));
                        double mStd = mStdObj != null ? mStdObj : 1.0;

                        for (int i = 0; i < betaM.length; i++) {
                            tM[i] = betaM[i] / seM[i];
                            pM[i] = 2 * (1 - tDistM.cumulativeProbability(Math.abs(tM[i])));
                            if (i > 0) {
                                if (i <= xDim) {
                                    // X变量的标准化系数
                                    Double xStdObj = calculateStd(xv.get(i - 1));
                                    double xStd = xStdObj != null ? xStdObj : 1.0;
                                    stdBetaM[i] = betaM[i] * xStd / mStd;
                                } else {
                                    // 中介变量的标准化系数
                                    int mIdx = i - xDim - 1;
                                    Double mPrevStdObj = calculateStd(mv.get(mIdx));
                                    double mPrevStd = mPrevStdObj != null ? mPrevStdObj : 1.0;
                                    stdBetaM[i] = betaM[i] * mPrevStd / mStd;
                                }
                            }
                        }

                        // 计算F统计量
                        double mseM = regM.calculateResidualSumOfSquares() / dfM;
                        double msrM = (regM.calculateTotalSumOfSquares() - regM.calculateResidualSumOfSquares()) / numPredictors;
                        double fStatM = msrM / mseM;
                        org.apache.commons.math3.distribution.FDistribution fDistM = new org.apache.commons.math3.distribution.FDistribution(numPredictors, dfM);
                        double fPM = 1 - fDistM.cumulativeProbability(fStatM);

                        allBetas.add(betaM);
                        allSEs.add(seM);
                        allTs.add(tM);
                        allPs.add(pM);
                        allStdBetas.add(stdBetaM);
                        allR2s.add(r2_M);
                        allAdjR2s.add(adjR2_M);
                        allFStats.add(String.format("F(%d,%d)=%.3f,p=%.3f", numPredictors, dfM, fStatM, fPM));

                    } else {
                        // 平行中介：只包含X变量（原始逻辑）
                        org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regM = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                        regM.newSampleData(mArray, xMatrix1);
                        double[] betaM = regM.estimateRegressionParameters();
                        double[] seM = regM.estimateRegressionParametersStandardErrors();
                        double r2_M = regM.calculateRSquared();
                        double adjR2_M = regM.calculateAdjustedRSquared();

                        // 计算t值、p值和标准化系数
                        org.apache.commons.math3.distribution.TDistribution tDistM = new org.apache.commons.math3.distribution.TDistribution(N - xDim - 1);
                        double[] tM = new double[betaM.length];
                        double[] pM = new double[betaM.length];
                        double[] stdBetaM = new double[betaM.length];
                        stdBetaM[0] = Double.NaN; // 常数项

                        Double mStdObj = calculateStd(mv.get(m));
                        double mStd = mStdObj != null ? mStdObj : 1.0;

                        for (int i = 0; i < betaM.length; i++) {
                            tM[i] = betaM[i] / seM[i];
                            pM[i] = 2 * (1 - tDistM.cumulativeProbability(Math.abs(tM[i])));
                            if (i > 0) {
                                Double xStdObj = calculateStd(xv.get(i - 1));
                                double xStd = xStdObj != null ? xStdObj : 1.0;
                                stdBetaM[i] = betaM[i] * xStd / mStd;
                            }
                        }

                        // 计算F统计量
                        double mseM = regM.calculateResidualSumOfSquares() / (N - xDim - 1);
                        double msrM = (regM.calculateTotalSumOfSquares() - regM.calculateResidualSumOfSquares()) / xDim;
                        double fStatM = msrM / mseM;
                        org.apache.commons.math3.distribution.FDistribution fDistM = new org.apache.commons.math3.distribution.FDistribution(xDim, N - xDim - 1);
                        double fPM = 1 - fDistM.cumulativeProbability(fStatM);

                        allBetas.add(betaM);
                        allSEs.add(seM);
                        allTs.add(tM);
                        allPs.add(pM);
                        allStdBetas.add(stdBetaM);
                        allR2s.add(r2_M);
                        allAdjR2s.add(adjR2_M);
                        allFStats.add(String.format("F(%d,%d)=%.3f,p=%.3f", xDim, N-xDim-1, fStatM, fPM));
                    }
                }

                // 最后一个模型: Y ~ X + M (直接效应和b路径)
                double[][] xmMatrix = new double[N][xDim + mediatorVars.size()];
                for (int i = 0; i < N; i++) {
                    for (int j = 0; j < xDim; j++) {
                        xmMatrix[i][j] = xv.get(j).get(i);
                    }
                    for (int j = 0; j < mediatorVars.size(); j++) {
                        xmMatrix[i][xDim + j] = mv.get(j).get(i);
                    }
                }

                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regFinal = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regFinal.newSampleData(yArray, xmMatrix);
                double[] betaFinal = regFinal.estimateRegressionParameters();
                double[] seFinal = regFinal.estimateRegressionParametersStandardErrors();
                double r2_Final = regFinal.calculateRSquared();
                double adjR2_Final = regFinal.calculateAdjustedRSquared();

                // 计算t值、p值和标准化系数
                int finalDf = N - xDim - mediatorVars.size() - 1;
                org.apache.commons.math3.distribution.TDistribution tDistFinal = new org.apache.commons.math3.distribution.TDistribution(finalDf);
                double[] tFinal = new double[betaFinal.length];
                double[] pFinal = new double[betaFinal.length];
                double[] stdBetaFinal = new double[betaFinal.length];
                stdBetaFinal[0] = Double.NaN; // 常数项

                for (int i = 0; i < betaFinal.length; i++) {
                    tFinal[i] = betaFinal[i] / seFinal[i];
                    pFinal[i] = 2 * (1 - tDistFinal.cumulativeProbability(Math.abs(tFinal[i])));
                    if (i > 0) {
                        if (i <= xDim) {
                            // 自变量
                            Double xStdObj = calculateStd(xv.get(i - 1));
                            double xStd = xStdObj != null ? xStdObj : 1.0;
                            stdBetaFinal[i] = betaFinal[i] * xStd / yStd;
                        } else {
                            // 中介变量
                            Double mStdObj = calculateStd(mv.get(i - xDim - 1));
                            double mStd = mStdObj != null ? mStdObj : 1.0;
                            stdBetaFinal[i] = betaFinal[i] * mStd / yStd;
                        }
                    }
                }

                // 计算F统计量
                int numPredictors = xDim + mediatorVars.size();
                double mseFinal = regFinal.calculateResidualSumOfSquares() / finalDf;
                double msrFinal = (regFinal.calculateTotalSumOfSquares() - regFinal.calculateResidualSumOfSquares()) / numPredictors;
                double fStatFinal = msrFinal / mseFinal;
                org.apache.commons.math3.distribution.FDistribution fDistFinal = new org.apache.commons.math3.distribution.FDistribution(numPredictors, finalDf);
                double fPFinal = 1 - fDistFinal.cumulativeProbability(fStatFinal);

                allBetas.add(betaFinal);
                allSEs.add(seFinal);
                allTs.add(tFinal);
                allPs.add(pFinal);
                allStdBetas.add(stdBetaFinal);
                allR2s.add(r2_Final);
                allAdjR2s.add(adjR2_Final);
                allFStats.add(String.format("F(%d,%d)=%.3f,p=%.3f", numPredictors, finalDf, fStatFinal, fPFinal));

            } catch (Exception e) {
                log.warn("[mediationEffectAnalysis] 回归计算异常，使用默认值", e);
                // 填充默认值
                for (int m = 0; m < totalModels; m++) {
                    allBetas.add(new double[]{0.0, 0.0});
                    allSEs.add(new double[]{0.0, 0.0});
                    allTs.add(new double[]{0.0, 0.0});
                    allPs.add(new double[]{1.0, 1.0});
                    allStdBetas.add(new double[]{Double.NaN, 0.0});
                    allR2s.add(0.0);
                    allAdjR2s.add(0.0);
                    // 根据模型类型设置不同的F统计量格式
                    if (m < totalModels - 1) {
                        // 前面的模型（Y~X 和 M~X）
                        allFStats.add(String.format("F(%d,%d)=0.000,p=1.000", xDim, N-xDim-1));
                    } else {
                        // 最后的模型（Y~X+M）
                        allFStats.add(String.format("F(%d,%d)=0.000,p=1.000", xDim + mediatorVars.size(), N-xDim-mediatorVars.size()-1));
                    }
                }
            }

            // 填充变量行数据
            for (int varIdx = 0; varIdx < allVarNames.size(); varIdx++) {
                List<Object> row = new ArrayList<>();
                row.add(allVarNames.get(varIdx));

                // 为每个模型填充数据
                for (int modelIdx = 0; modelIdx < totalModels; modelIdx++) {
                    double[] betas = allBetas.get(modelIdx);
                    double[] ses = allSEs.get(modelIdx);
                    double[] ts = allTs.get(modelIdx);
                    double[] ps = allPs.get(modelIdx);
                    double[] stdBetas = allStdBetas.get(modelIdx);

                    // 根据变量类型和模型类型决定是否显示数据
                    boolean shouldShowData = false;
                    int betaIndex = -1;

                    if (varIdx == 0) { // 常数项
                        shouldShowData = true;
                        betaIndex = 0;
                    } else if (varIdx <= xNames.size()) { // 自变量
                        shouldShowData = true;
                        betaIndex = varIdx;
                    } else { // 中介变量
                        int mIdx = varIdx - xNames.size() - 1;
                        if ("chain".equals(mediationType)) {
                            // 链式中介：中介变量在对应的模型中显示
                            if (modelIdx > 1 && modelIdx <= mediatorVars.size() + 1) {
                                // 模型2到模型n+1对应中介变量M1到Mn
                                int currentMediatorModel = modelIdx - 2; // 当前模型对应的中介变量索引
                                if (mIdx <= currentMediatorModel) {
                                    // 当前中介变量或之前的中介变量在当前模型中显示
                                    shouldShowData = true;
                                    betaIndex = xNames.size() + 1 + mIdx;
                                }
                            } else if (modelIdx == totalModels - 1) {
                                // 最后一个模型包含所有中介变量
                                shouldShowData = true;
                                betaIndex = xNames.size() + 1 + mIdx;
                            }
                        } else {
                            // 平行中介：只在最后一个模型中显示中介变量
                            if (modelIdx == totalModels - 1) {
                                shouldShowData = true;
                                betaIndex = xNames.size() + 1 + mIdx;
                            }
                        }
                    }

                    if (shouldShowData && betaIndex < betas.length) {
                        row.add(String.format("%.3f", betas[betaIndex]));
                        row.add(String.format("%.3f", ses[betaIndex]));
                        row.add(String.format("%.3f", ts[betaIndex]));
                        String pStr = ps[betaIndex] < 0.01 ? String.format("%.3f**", ps[betaIndex]) :
                                     ps[betaIndex] < 0.05 ? String.format("%.3f*", ps[betaIndex]) :
                                     String.format("%.3f", ps[betaIndex]);
                        row.add(pStr);
                        if (Double.isNaN(stdBetas[betaIndex])) {
                            row.add("");
                        } else {
                            row.add(String.format("%.3f", stdBetas[betaIndex]));
                        }
                    } else {
                        // 不显示数据的情况
                        for (int i = 0; i < 5; i++) row.add("");
                    }
                }

                mergedRows.add(row);
            }

            // 添加模型统计信息
            List<Object> r2Row = new ArrayList<>();
            r2Row.add("R²");
            for (int m = 0; m < totalModels; m++) {
                if (m < allR2s.size()) {
                    r2Row.add(String.format("%.3f", allR2s.get(m)));
                } else {
                    r2Row.add("0.000");
                }
                for (int i = 1; i < 5; i++) r2Row.add("");
            }
            mergedRows.add(r2Row);

            List<Object> adjR2Row = new ArrayList<>();
            adjR2Row.add("调整R²");
            for (int m = 0; m < totalModels; m++) {
                if (m < allAdjR2s.size()) {
                    adjR2Row.add(String.format("%.3f", allAdjR2s.get(m)));
                } else {
                    adjR2Row.add("0.000");
                }
                for (int i = 1; i < 5; i++) adjR2Row.add("");
            }
            mergedRows.add(adjR2Row);

            List<Object> fRow = new ArrayList<>();
            fRow.add("F");
            for (int m = 0; m < totalModels; m++) {
                if (m < allFStats.size()) {
                    fRow.add(allFStats.get(m));
                } else {
                    fRow.add("F(1," + (N-2) + ")=0.000,p=0.000");
                }
                for (int i = 1; i < 5; i++) fRow.add("");
            }
            mergedRows.add(fRow);

            // 备注行
            List<Object> noteRow = new ArrayList<>();
            noteRow.add("备注：因变量 = " + dependentVar + "，" + yName);
            for (int i = 1; i < headRow1.size(); i++) {
                noteRow.add("");
            }
            mergedRows.add(noteRow);

            // 显著性说明行
            List<Object> ruleRow = new ArrayList<>();
            ruleRow.add("* p<0.05 ** p<0.01");
            for (int i = 1; i < headRow1.size(); i++) {
                ruleRow.add("");
            }
            mergedRows.add(ruleRow);

            // 设置单元格合并
            List<FactorAnalysisOutput.CellMerge> cellMerges = new ArrayList<>();
            // 一级表头合并
            cellMerges.add(newCellMerge(0, 0, 2, 1)); // 变量列
            int col = 1;
            for (int m = 0; m < totalModels; m++) {
                cellMerges.add(newCellMerge(0, col, 1, 5));
                col += 5;
            }

            // 统计量行合并
            for (int s = 0; s < 3; s++) { // R², 调整R², F
                int rowIdx = mergedRows.size() - 5 + s;
                col = 1;
                for (int m = 0; m < totalModels; m++) {
                    cellMerges.add(newCellMerge(rowIdx, col, 1, 5));
                    col += 5;
                }
            }

            // 备注和显著性说明行合并
            cellMerges.add(newCellMerge(mergedRows.size() - 2, 0, 1, headRow1.size()));
            cellMerges.add(newCellMerge(mergedRows.size() - 1, 0, 1, headRow1.size()));

            // 设置单元格样式
            List<List<FactorAnalysisOutput.CellStyle>> cellStyles = new ArrayList<>();
            for (int i = 0; i < mergedRows.size(); i++) {
                List<FactorAnalysisOutput.CellStyle> styleRow = new ArrayList<>();
                for (int j = 0; j < headRow1.size(); j++) {
                    if (i == 0 || i == 1 || i >= mergedRows.size() - 2) {
                        styleRow.add(newCellStyle("bold", null));
                    } else {
                        styleRow.add(newCellStyle(null, null));
                    }
                }
                cellStyles.add(styleRow);
            }

            // 组装TableData
            FactorAnalysisOutput.TableData table = new FactorAnalysisOutput.TableData();
            table.setType("mediation_analysis");
            table.setTitle("中介效应分析结果 (n=" + N + ")");
            table.setHeaders(null);
            table.setRows(mergedRows);
            table.setCellMerges(cellMerges);
            table.setCellStyles(cellStyles);

            List<FactorAnalysisOutput.TableData> resultTables = new ArrayList<>();
            resultTables.add(table);
            saveTableDataToMessage(sessionId, resultTables);
            return resultTables;
        } catch (Exception e) {
            log.error("[mediationEffectAnalysis] 中介效应分析异常", e);
            throw new RuntimeException("中介效应分析异常: " + e.getMessage());
        }
    }

    // 计算标准差
    private double std(double[] arr) {
        double mean = 0.0;
        for (double v : arr)
            mean += v;
        mean /= arr.length;
        double sum = 0.0;
        for (double v : arr)
            sum += (v - mean) * (v - mean);
        return Math.sqrt(sum / (arr.length - 1));
    }

    /**
     * 执行平行中介分析
     */
    private void performParallelMediationAnalysis(List<Double> yv, List<List<Double>> xv, List<List<Double>> mv,
            int N, int xDim, double[] a, double[] b, double[] ab, double[] ab_se, double[] ab_z,
            double[] ab_p, double[] ab_ci_low, double[] ab_ci_high) {

        // 平行中介：每个中介变量独立地从X到Y
        // 3.1 X->M (a路径)
        for (int i = 0; i < mv.size(); i++) {
            double[] M = new double[N];
            double[][] Xmat = new double[N][xDim];
            for (int j = 0; j < N; j++) {
                M[j] = mv.get(i).get(j);
                for (int d = 0; d < xDim; d++)
                    Xmat[j][d] = xv.get(d).get(j);
            }
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXM = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            regXM.newSampleData(M, Xmat);
            double[] beta = regXM.estimateRegressionParameters();
            a[i] = beta[1]; // 取第一个自变量的系数
        }

        // 3.2 X,M->Y (b路径)
        double[][] XM = new double[N][xDim + mv.size()];
        for (int j = 0; j < N; j++) {
            for (int d = 0; d < xDim; d++)
                XM[j][d] = xv.get(d).get(j);
            for (int k = 0; k < mv.size(); k++)
                XM[j][xDim + k] = mv.get(k).get(j);
        }
        org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXMY = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
        double[] Y = new double[N];
        for (int j = 0; j < N; j++)
            Y[j] = yv.get(j);
        regXMY.newSampleData(Y, XM);
        double[] betaXMY = regXMY.estimateRegressionParameters();
        for (int k = 0; k < mv.size(); k++)
            b[k] = betaXMY[xDim + k + 1];

        // 计算间接效应和Bootstrap置信区间
        calculateIndirectEffects(yv, xv, mv, N, xDim, a, b, ab, ab_se, ab_z, ab_p, ab_ci_low, ab_ci_high);
    }

    /**
     * 执行链式中介分析
     */
    private void performChainMediationAnalysis(List<Double> yv, List<List<Double>> xv, List<List<Double>> mv,
            int N, int xDim, double[] a, double[] b, double[] ab, double[] ab_se, double[] ab_z,
            double[] ab_p, double[] ab_ci_low, double[] ab_ci_high) {

        // 链式中介：X -> M1 -> M2 -> ... -> Y
        if (mv.size() < 2) {
            // 如果只有一个中介变量，退化为简单中介
            performParallelMediationAnalysis(yv, xv, mv, N, xDim, a, b, ab, ab_se, ab_z, ab_p, ab_ci_low, ab_ci_high);
            return;
        }

        // 链式中介需要计算每个连续的路径
        // 对于链式中介，我们需要重新理解a和b的含义：
        // a[i] = 从前一个变量到当前中介变量的系数
        // b[i] = 从当前中介变量到后一个变量的系数

        // 第一步：X -> M1 (a[0])
        double[] M1 = new double[N];
        double[][] Xmat = new double[N][xDim];
        for (int j = 0; j < N; j++) {
            M1[j] = mv.get(0).get(j);
            for (int d = 0; d < xDim; d++)
                Xmat[j][d] = xv.get(d).get(j);
        }
        org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXM1 = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
        regXM1.newSampleData(M1, Xmat);
        double[] betaXM1 = regXM1.estimateRegressionParameters();
        a[0] = betaXM1[1]; // X -> M1

        // 中间步骤：Mi-1 -> Mi (a[i] for i > 0)
        for (int i = 1; i < mv.size(); i++) {
            double[] Mi = new double[N];
            double[][] MiPrevMat = new double[N][1];
            for (int j = 0; j < N; j++) {
                Mi[j] = mv.get(i).get(j);
                MiPrevMat[j][0] = mv.get(i-1).get(j);
            }
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regMiPrevMi = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            regMiPrevMi.newSampleData(Mi, MiPrevMat);
            double[] betaMiPrevMi = regMiPrevMi.estimateRegressionParameters();
            a[i] = betaMiPrevMi[1]; // Mi-1 -> Mi
        }

        // 现在计算b系数：每个中介变量到其后续变量的系数
        // 对于链式中介，b[i] = a[i+1]，除了最后一个
        for (int i = 0; i < mv.size() - 1; i++) {
            b[i] = a[i + 1]; // Mi -> Mi+1的系数就是a[i+1]
        }

        // 最后一个中介变量到Y的系数 (b[mv.size()-1])
        double[] Y = new double[N];
        double[][] MnMat = new double[N][1];
        for (int j = 0; j < N; j++) {
            Y[j] = yv.get(j);
            MnMat[j][0] = mv.get(mv.size()-1).get(j);
        }
        org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regMnY = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
        regMnY.newSampleData(Y, MnMat);
        double[] betaMnY = regMnY.estimateRegressionParameters();
        b[mv.size()-1] = betaMnY[1]; // Mn -> Y

        // 计算每个中介变量的间接效应
        // 对于链式中介，每个中介变量的间接效应是从该变量开始到Y的所有路径的乘积
        for (int i = 0; i < mv.size(); i++) {
            ab[i] = a[i]; // 先设为a系数
            // 然后乘以从当前中介变量到Y的所有后续路径系数
            for (int j = i; j < mv.size(); j++) {
                if (j == i) {
                    ab[i] *= b[j];
                } else {
                    ab[i] *= a[j] * b[j];
                }
            }
        }

        // 计算Bootstrap置信区间
        calculateChainIndirectEffects(yv, xv, mv, N, xDim, ab, ab_se, ab_z, ab_p, ab_ci_low, ab_ci_high);
    }

    /**
     * 计算间接效应和Bootstrap置信区间（平行中介）
     */
    private void calculateIndirectEffects(List<Double> yv, List<List<Double>> xv, List<List<Double>> mv,
            int N, int xDim, double[] a, double[] b, double[] ab, double[] ab_se, double[] ab_z,
            double[] ab_p, double[] ab_ci_low, double[] ab_ci_high) {

        // Bootstrap置信区间
        int bootN = 1000;
        double[][] abBoot = new double[mv.size()][bootN];
        java.util.Random rand = new java.util.Random();

        for (int bidx = 0; bidx < bootN; bidx++) {
            int[] idxs = new int[N];
            for (int i = 0; i < N; i++)
                idxs[i] = rand.nextInt(N);

            // X->M
            double[] a_b = new double[mv.size()];
            for (int i = 0; i < mv.size(); i++) {
                double[] Mb = new double[N];
                double[][] Xmatb = new double[N][xDim];
                for (int j = 0; j < N; j++) {
                    Mb[j] = mv.get(i).get(idxs[j]);
                    for (int d = 0; d < xDim; d++)
                        Xmatb[j][d] = xv.get(d).get(idxs[j]);
                }
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXM = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regXM.newSampleData(Mb, Xmatb);
                double[] beta = regXM.estimateRegressionParameters();
                a_b[i] = beta[1];
            }

            // X,M->Y
            double[][] XMb = new double[N][xDim + mv.size()];
            for (int j = 0; j < N; j++) {
                for (int d = 0; d < xDim; d++)
                    XMb[j][d] = xv.get(d).get(idxs[j]);
                for (int k = 0; k < mv.size(); k++)
                    XMb[j][xDim + k] = mv.get(k).get(idxs[j]);
            }
            double[] Yb = new double[N];
            for (int j = 0; j < N; j++)
                Yb[j] = yv.get(idxs[j]);
            org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXMYb = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
            regXMYb.newSampleData(Yb, XMb);
            double[] betaXMYb = regXMYb.estimateRegressionParameters();
            for (int k = 0; k < mv.size(); k++) {
                abBoot[k][bidx] = a_b[k] * betaXMYb[xDim + k + 1];
            }
        }

        // 计算间接效应统计量
        for (int k = 0; k < mv.size(); k++) {
            ab[k] = a[k] * b[k];
            double[] boots = abBoot[k];
            java.util.Arrays.sort(boots);
            ab_ci_low[k] = boots[(int) (bootN * 0.025)];
            ab_ci_high[k] = boots[(int) (bootN * 0.975)];
            ab_se[k] = std(boots);
            ab_z[k] = ab[k] / (ab_se[k] + 1e-10);
            ab_p[k] = 2 * (1 - new org.apache.commons.math3.distribution.NormalDistribution()
                    .cumulativeProbability(Math.abs(ab_z[k])));
        }
    }

    /**
     * 计算链式中介的间接效应和Bootstrap置信区间
     */
    private void calculateChainIndirectEffects(List<Double> yv, List<List<Double>> xv, List<List<Double>> mv,
            int N, int xDim, double[] ab, double[] ab_se, double[] ab_z,
            double[] ab_p, double[] ab_ci_low, double[] ab_ci_high) {

        // 为每个中介变量计算Bootstrap置信区间
        int bootN = 1000;
        double[][] abBoot = new double[mv.size()][bootN];
        java.util.Random rand = new java.util.Random();

        for (int bidx = 0; bidx < bootN; bidx++) {
            int[] idxs = new int[N];
            for (int i = 0; i < N; i++)
                idxs[i] = rand.nextInt(N);

            try {
                // 重新计算所有路径系数
                double[] a_boot = new double[mv.size()];
                double[] b_boot = new double[mv.size()];

                // X -> M1
                double[] M1b = new double[N];
                double[][] Xmatb = new double[N][xDim];
                for (int j = 0; j < N; j++) {
                    M1b[j] = mv.get(0).get(idxs[j]);
                    for (int d = 0; d < xDim; d++)
                        Xmatb[j][d] = xv.get(d).get(idxs[j]);
                }
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regXM1b = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regXM1b.newSampleData(M1b, Xmatb);
                double[] betaXM1b = regXM1b.estimateRegressionParameters();
                a_boot[0] = betaXM1b[1];

                // Mi-1 -> Mi
                for (int i = 1; i < mv.size(); i++) {
                    double[] Mib = new double[N];
                    double[][] MiPrevMatb = new double[N][1];
                    for (int j = 0; j < N; j++) {
                        Mib[j] = mv.get(i).get(idxs[j]);
                        MiPrevMatb[j][0] = mv.get(i-1).get(idxs[j]);
                    }
                    org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regMiPrevMib = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                    regMiPrevMib.newSampleData(Mib, MiPrevMatb);
                    double[] betaMiPrevMib = regMiPrevMib.estimateRegressionParameters();
                    a_boot[i] = betaMiPrevMib[1];
                }

                // 计算b系数
                for (int i = 0; i < mv.size() - 1; i++) {
                    b_boot[i] = a_boot[i + 1];
                }

                // Mn -> Y
                double[] Yb = new double[N];
                double[][] MnMatb = new double[N][1];
                for (int j = 0; j < N; j++) {
                    Yb[j] = yv.get(idxs[j]);
                    MnMatb[j][0] = mv.get(mv.size()-1).get(idxs[j]);
                }
                org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression regMnYb = new org.apache.commons.math3.stat.regression.OLSMultipleLinearRegression();
                regMnYb.newSampleData(Yb, MnMatb);
                double[] betaMnYb = regMnYb.estimateRegressionParameters();
                b_boot[mv.size()-1] = betaMnYb[1];

                // 计算每个中介变量的间接效应
                for (int i = 0; i < mv.size(); i++) {
                    double effect = a_boot[i];
                    for (int j = i; j < mv.size(); j++) {
                        if (j == i) {
                            effect *= b_boot[j];
                        } else {
                            effect *= a_boot[j] * b_boot[j];
                        }
                    }
                    abBoot[i][bidx] = effect;
                }

            } catch (Exception e) {
                // 如果计算失败，设为0
                for (int i = 0; i < mv.size(); i++) {
                    abBoot[i][bidx] = 0.0;
                }
            }
        }

        // 计算每个中介变量的统计量
        for (int i = 0; i < mv.size(); i++) {
            double[] boots = abBoot[i];
            java.util.Arrays.sort(boots);
            ab_ci_low[i] = boots[(int) (bootN * 0.025)];
            ab_ci_high[i] = boots[(int) (bootN * 0.975)];
            ab_se[i] = std(boots);
            ab_z[i] = ab[i] / (ab_se[i] + 1e-10);
            ab_p[i] = 2 * (1 - new org.apache.commons.math3.distribution.NormalDistribution()
                    .cumulativeProbability(Math.abs(ab_z[i])));
        }
    }

    /**
     * 获取定类变量的字符串数据
     */
    private List<String> getCategoricalColumnData(String sessionId, int colIndex) {
        try {
            AiChatMessage latestMessage = aiChatService.findLatestAssistantMessageWithExcel(sessionId);
            if (latestMessage == null || latestMessage.getCompleteExcelData() == null) {
                throw new RuntimeException("未找到Excel数据");
            }

            ObjectMapper objectMapper = new ObjectMapper();
            List<List<String>> excelData = objectMapper.readValue(latestMessage.getCompleteExcelData(),
                    new TypeReference<List<List<String>>>() {});

            if (excelData.size() <= 1) {
                throw new RuntimeException("Excel数据不足");
            }

            List<String> columnData = new ArrayList<>();
            for (int i = 1; i < excelData.size(); i++) { // 跳过表头
                List<String> row = excelData.get(i);
                if (colIndex < row.size()) {
                    String value = row.get(colIndex);
                    columnData.add(value != null ? value.trim() : "");
                } else {
                    columnData.add("");
                }
            }
            return columnData;
        } catch (Exception e) {
            log.error("获取定类变量数据失败", e);
            throw new RuntimeException("获取定类变量数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建哑变量编码
     * @param categoricalData 定类数据
     * @return 哑变量列表（去掉参考组）
     */
    private List<List<Double>> createDummyVariables(List<String> categoricalData) {
        // 获取所有唯一值并排序
        Set<String> uniqueValues = new LinkedHashSet<>(categoricalData);
        List<String> sortedValues = new ArrayList<>(uniqueValues);
        Collections.sort(sortedValues);

        // 创建哑变量（去掉第一个作为参考组）
        List<List<Double>> dummyVars = new ArrayList<>();
        for (int i = 1; i < sortedValues.size(); i++) { // 从第二个开始，第一个作为参考组
            List<Double> dummyVar = new ArrayList<>();
            String targetValue = sortedValues.get(i);
            for (String value : categoricalData) {
                dummyVar.add(targetValue.equals(value) ? 1.0 : 0.0);
            }
            dummyVars.add(dummyVar);
        }

        return dummyVars;
    }
}